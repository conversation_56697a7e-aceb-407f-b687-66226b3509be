package com.atp.product.controller;

import com.atp.product.controller.dto.request.InternalPurchaseOrderRequest;
import com.atp.product.controller.dto.response.PurchaseOrderResponse;
import com.atp.product.service.impl.CommonServiceImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/purchase-order")
public class karmakPOController {

    private final CommonServiceImpl commonService;

    public karmakPOController(CommonServiceImpl commonService) {
        this.commonService = commonService;
    }

    @PostMapping("/create")
    public ResponseEntity<PurchaseOrderResponse> createPurchaseOrder(@RequestBody InternalPurchaseOrderRequest internalRequest) {
        PurchaseOrderResponse response = commonService.createPurchaseOrder(internalRequest);
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

}
