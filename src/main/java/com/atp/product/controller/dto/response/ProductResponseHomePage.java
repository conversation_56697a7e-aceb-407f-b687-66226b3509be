package com.atp.product.controller.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductResponseHomePage {
    private String productId;
    private String productName;
    private String categoryName;
    private double price = 0.0;
    private String primaryImage;
    private Date lastModifiedDate;
    private String partNumber;
    private String partNumberSlug;
    private Map<String, String>  assetMap;
    private int quantity = 0;
    private String description;
    private int productQuantity; // Product quantity display in cart view and wishlist
    private double rowTotal;
    private boolean isInWishlist;
    private String vendorName;
    private List<String> crossReferenceId;
    private int karmakPartId;
    private String branch;
}
