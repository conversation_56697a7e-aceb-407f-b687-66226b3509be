package com.atp.product.controller.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * Request DTO for creating scheduled orders
 */
@Data
public class ScheduledOrderRequest {

    @JsonProperty("karmakCustomerId")
    @NotBlank(message = "Karmak Customer ID is required")
    private String karmakCustomerId;

    @JsonProperty("customerCorrelationId")
    @NotBlank(message = "Customer Correlation ID is required")
    private String customerCorrelationId;

    @JsonProperty("originalOrderId")
    @NotBlank(message = "Original order ID is required")
    private String originalOrderId;

    @JsonProperty("email")
    @Email(message = "Email should be valid")
    private String email;

    @JsonProperty("name")
    private String name;

    @JsonProperty("frequencyDays")
    @NotNull(message = "Frequency in days is required")
    @Min(value = 1, message = "Frequency must be at least 1 day")
    private Integer frequencyDays;

    @JsonProperty("notes")
    private String notes;

    @JsonProperty("notifyOnOutOfStock")
    private Boolean notifyOnOutOfStock = true;
}
