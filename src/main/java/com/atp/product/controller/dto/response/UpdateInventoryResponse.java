package com.atp.product.controller.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class UpdateInventoryResponse {
    @JsonProperty("Branch")
    private String branch;
    @JsonProperty("PartNumber")
    private String partNumber;
    @JsonProperty("Supplier")
    private String supplier;
    @JsonProperty("Status")
    private String status;
    @JsonProperty("Message")
    private String message;

}
