package com.atp.product.controller.dto.response;

import com.atp.product.model.KeyValue;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductDetailsResponse {
    private String catalogId;
    private String name;
    private String description;
    private String longDescription;
    private double price;
    private String primaryImage;
    private List<String> alternateImages;
    private List<KeyValue> specificationAttributes;
    private String partNumber;
    private int quantity;
    private String brandId;
    private String brandName;
    private String vendorId;
    private String vendorName;
    private List<String> model;
    private List<String> year;
    private double height;
    private double length;
    private double weight;
    private double width;
    private List<String> make;
    private List<String> featuredAndBenifits;
    private String category;
    private String subCategory;
    private List<String> applicationSummary;
    private String categorySlug;
    private String brandSlug;
    private Map<String, String> assetMap;
    private Map<String, String> assetMediumFullImage; //Key : 400X400 Size Value: Full Image
    private Map<String, String> assetSmallMediumImage; //Key : 200X200 Size Value: 400X400
    private String brandLogoUrl;
    private boolean isInWishlist;
    private String subCategorySlug;
    private List<String> crossReferenceId;
    private int karmakPartId;
    private String branch;
}
