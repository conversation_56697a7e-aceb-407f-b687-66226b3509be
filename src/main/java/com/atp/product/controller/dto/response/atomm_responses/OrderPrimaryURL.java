package com.atp.product.controller.dto.response.atomm_responses;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "attributeId",
        "content"
})
public class OrderPrimaryURL {

    @JsonProperty("attributeId")
    private String attributeId;
    @JsonProperty("content")
    private List<Content> content;

    @JsonProperty("attributeId")
    public String getAttributeId() {
        return attributeId;
    }

    @JsonProperty("attributeId")
    public void setAttributeId(String attributeId) {
        this.attributeId = attributeId;
    }

    @JsonProperty("content")
    public List<Content> getContent() {
        return content;
    }

    @JsonProperty("content")
    public void setContent(List<Content> content) {
        this.content = content;
    }

}
