package com.atp.product.controller;

import com.atp.product.controller.dto.request.ScheduledOrderRequest;
import com.atp.product.controller.dto.request.UpdateScheduleRequest;
import com.atp.product.controller.dto.response.CommonResponse;
import com.atp.product.controller.dto.response.ScheduledOrderResponse;
import com.atp.product.exception.bad_request.ScheduledOrderAlreadyExistsException;
import com.atp.product.model.ScheduledOrder;
import com.atp.product.service.ScheduledOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * REST Controller for managing scheduled orders
 */
@Slf4j
@RestController
@RequestMapping("/api/scheduled-orders")
@Tag(name = "Scheduled Orders", description = "API for managing scheduled orders and subscriptions")
public class ScheduledOrderController {

    private final ScheduledOrderService scheduledOrderService;
    private final MongoTemplate mongoTemplate;

    @Autowired
    public ScheduledOrderController(ScheduledOrderService scheduledOrderService, MongoTemplate mongoTemplate) {
        this.scheduledOrderService = scheduledOrderService;
        this.mongoTemplate = mongoTemplate;
    }

    @Operation(summary = "Subscribe to scheduled orders", 
               description = "Create a new scheduled order subscription for automatic recurring orders")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Scheduled order created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "404", description = "Original order not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/subscribe")
    public ResponseEntity<CommonResponse> subscribe(
            @Valid @RequestBody ScheduledOrderRequest request) {

        try {
            ScheduledOrderResponse response = scheduledOrderService.subscribe(request);
            return ResponseEntity
                    .status(HttpStatus.CREATED)
                    .body(new CommonResponse(true, "Scheduled order subscription created or reactivated successfully.", response));

        } catch (ScheduledOrderAlreadyExistsException e) {
            log.warn("Subscription already exists: {}", e.getMessage());
            return ResponseEntity
                    .ok()
                    .body(new CommonResponse(false, e.getMessage(), null));

        }
    }

    @Operation(summary = "Cancel scheduled order", 
               description = "Cancel an existing scheduled order subscription")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Scheduled order cancelled successfully"),
        @ApiResponse(responseCode = "404", description = "Scheduled order not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @DeleteMapping("/{scheduledOrderId}")
    public ResponseEntity<Void> cancel(
            @Parameter(description = "Scheduled order ID") @PathVariable String scheduledOrderId,
            @Parameter(description = "Karmak Customer ID") @RequestParam String karmakCustomerId) {

        log.info("Received cancellation request for scheduled order: {} by customer: {}",
                scheduledOrderId, karmakCustomerId);

        try {
            boolean cancelled = scheduledOrderService.cancel(scheduledOrderId, karmakCustomerId);
            
            if (cancelled) {
                return ResponseEntity.ok().build();
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("Error cancelling scheduled order: {}", scheduledOrderId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "Update scheduled order", 
               description = "Update scheduling parameters for an existing scheduled order")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Scheduled order updated successfully"),
        @ApiResponse(responseCode = "404", description = "Scheduled order not found"),
        @ApiResponse(responseCode = "400", description = "Invalid update data"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PutMapping("/{scheduledOrderId}")
    public ResponseEntity<ScheduledOrderResponse> updateSchedule(
            @Parameter(description = "Scheduled order ID") @PathVariable String scheduledOrderId,
            @Valid @RequestBody UpdateScheduleRequest request) {
        
        log.info("Received update request for scheduled order: {}", scheduledOrderId);
        
        try {
            ScheduledOrderResponse response = scheduledOrderService.updateSchedule(scheduledOrderId, request);
            
            if (response != null) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("Error updating scheduled order: {}", scheduledOrderId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "Get scheduled orders by customer", 
               description = "Retrieve all scheduled orders for a specific customer")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Scheduled orders retrieved successfully"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/customer/{karmakCustomerId}")
    public ResponseEntity<List<ScheduledOrderResponse>> getScheduledOrdersByCustomer(
            @Parameter(description = "Karmak Customer ID") @PathVariable String karmakCustomerId) {

        log.info("Received request to get scheduled orders for customer: {}", karmakCustomerId);

        try {
            List<ScheduledOrderResponse> orders = scheduledOrderService.getScheduledOrdersByCustomer(karmakCustomerId);
            return ResponseEntity.ok(orders);

        } catch (Exception e) {
            log.error("Error retrieving scheduled orders for customer: {}", karmakCustomerId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "Get scheduled order by ID", 
               description = "Retrieve a specific scheduled order by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Scheduled order retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Scheduled order not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/{scheduledOrderId}")
    public ResponseEntity<ScheduledOrderResponse> getScheduledOrderById(
            @Parameter(description = "Scheduled order ID") @PathVariable String scheduledOrderId,
            @Parameter(description = "Karmak Customer ID") @RequestParam String karmakCustomerId) {

        log.info("Received request to get scheduled order: {} for customer: {}", scheduledOrderId, karmakCustomerId);

        try {
            ScheduledOrderResponse order = scheduledOrderService.getScheduledOrderById(scheduledOrderId, karmakCustomerId);
            
            if (order != null) {
                return ResponseEntity.ok(order);
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("Error retrieving scheduled order: {}", scheduledOrderId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }



    @Operation(summary = "Get all active scheduled orders", 
               description = "Retrieve all active scheduled orders (admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Active scheduled orders retrieved successfully"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/admin/active")
    public ResponseEntity<List<ScheduledOrderResponse>> getAllActiveScheduledOrders() {
        
        log.info("Received request to get all active scheduled orders");
        
        try {
            List<ScheduledOrderResponse> orders = scheduledOrderService.getAllActiveScheduledOrders();
            return ResponseEntity.ok(orders);
            
        } catch (Exception e) {
            log.error("Error retrieving all active scheduled orders", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "Trigger scheduled order processing", 
               description = "Manually trigger the processing of due scheduled orders (admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Scheduled order processing triggered successfully"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/admin/process")
    public ResponseEntity<Void> triggerScheduledOrderProcessing() {
        
        log.info("Received request to manually trigger scheduled order processing");
        
        try {
            scheduledOrderService.processScheduledOrders();
            return ResponseEntity.ok().build();
            
        } catch (Exception e) {
            log.error("Error triggering scheduled order processing", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * TEST ENDPOINT: Manually trigger OUT_OF_STOCK orders check
     * For testing and debugging purposes
     */
    @PostMapping("/test/check-out-of-stock")
    public ResponseEntity<CommonResponse> testCheckOutOfStockOrders() {
        log.info("TEST API: Manually triggering checkAndUpdateOutOfStockOrders");

        try {
            // Get counts before processing
            long outOfStockCountBefore = getOutOfStockOrdersCount();
            long activeCountBefore = getActiveOrdersCount();

            LocalDateTime startTime = LocalDateTime.now();

            // Trigger the method
            scheduledOrderService.checkAndUpdateOutOfStockOrders();

            LocalDateTime endTime = LocalDateTime.now();

            // Get counts after processing
            long outOfStockCountAfter = getOutOfStockOrdersCount();
            long activeCountAfter = getActiveOrdersCount();

            // Calculate changes
            long ordersUpdatedToActive = outOfStockCountBefore - outOfStockCountAfter;
            long processingTimeMs = java.time.Duration.between(startTime, endTime).toMillis();

            // Prepare response
            Map<String, Object> result = new HashMap<>();
            result.put("processingStartTime", startTime);
            result.put("processingEndTime", endTime);
            result.put("processingTimeMs", processingTimeMs);
            result.put("outOfStockOrdersBefore", outOfStockCountBefore);
            result.put("outOfStockOrdersAfter", outOfStockCountAfter);
            result.put("activeOrdersBefore", activeCountBefore);
            result.put("activeOrdersAfter", activeCountAfter);
            result.put("ordersUpdatedToActive", ordersUpdatedToActive);
            result.put("status", "SUCCESS");

            log.info("TEST API: Processing completed. {} orders updated from OUT_OF_STOCK to ACTIVE",
                    ordersUpdatedToActive);

            CommonResponse response = new CommonResponse();
            response.setSuccess(true);
            response.setMessage("OUT_OF_STOCK orders check completed successfully");
            response.setData(result);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("TEST API: Error in checkAndUpdateOutOfStockOrders", e);

            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("status", "ERROR");
            errorResult.put("error", e.getMessage());
            errorResult.put("timestamp", LocalDateTime.now());

            CommonResponse response = new CommonResponse();
            response.setSuccess(false);
            response.setMessage("Error processing OUT_OF_STOCK orders: " + e.getMessage());
            response.setData(errorResult);

            return ResponseEntity.status(500).body(response);
        }
    }

    // Helper methods
    private long getOutOfStockOrdersCount() {
        Query query = new Query(Criteria.where("active").is(true)
                .and("status").is("OUT_OF_STOCK"));
        return mongoTemplate.count(query, ScheduledOrder.class);
    }

    private long getActiveOrdersCount() {
        Query query = new Query(Criteria.where("active").is(true)
                .and("status").is("ACTIVE"));
        return mongoTemplate.count(query, ScheduledOrder.class);
    }

    private long getTotalActiveOrdersCount() {
        Query query = new Query(Criteria.where("active").is(true));
        return mongoTemplate.count(query, ScheduledOrder.class);
    }
}

