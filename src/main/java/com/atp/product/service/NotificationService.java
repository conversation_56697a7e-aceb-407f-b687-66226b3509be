package com.atp.product.service;

import com.atp.product.model.ScheduledOrder;

import java.util.List;

/**
 * Service for handling email notifications related to scheduled orders
 */
public interface NotificationService {

    /**
     * Send email notification when scheduled order items are out of stock
     * @param scheduledOrder The complete scheduled order with all details
     * @param unavailableParts List of part numbers that are out of stock
     */
    void sendOutOfStockEmail(ScheduledOrder scheduledOrder, List<String> unavailableParts);

    /**
     * Send email confirmation when scheduled order is created
     */
    void sendScheduledOrderConfirmation(String email, String customerName, String customerId, String scheduledOrderId, String nextRunDate);

    /**
     * Send email notification when scheduled order is cancelled
     */
    void sendScheduledOrderCancellation(String email, String customerName, String customerId, String scheduledOrderId);

    /**
     * Send email notification when scheduled order is updated
     */
    void sendScheduledOrderUpdate(String email, String customerName, String customerId, String scheduledOrderId, int newFrequency);

    /**
     * Send email notification when purchase order is created successfully
     */
    void sendPurchaseOrderSuccess(String email, String customerName, String customerId, String poNumber, String scheduledOrderId);

    /**
     * Send email notification when purchase order creation fails
     */
    void sendPurchaseOrderFailure(String email, String customerName, String customerId, String scheduledOrderId, String errorMessage);
}
