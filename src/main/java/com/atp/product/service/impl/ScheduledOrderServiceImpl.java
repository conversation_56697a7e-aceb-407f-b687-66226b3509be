package com.atp.product.service.impl;

import com.atp.product.controller.dto.request.InternalPurchaseOrderRequest;
import com.atp.product.controller.dto.request.ScheduledOrderRequest;
import com.atp.product.controller.dto.request.UpdateScheduleRequest;
import com.atp.product.controller.dto.response.PurchaseOrderResponse;
import com.atp.product.controller.dto.response.ScheduledOrderResponse;
import com.atp.product.controller.dto.response.atomm_responses.*;
import com.atp.product.exception.DomainException;
import com.atp.product.exception.bad_request.KarmakApiException;
import com.atp.product.exception.bad_request.ScheduledOrderAlreadyExistsException;
import com.atp.product.model.ScheduledOrder;
import com.atp.product.service.NotificationService;
import com.atp.product.service.ScheduledOrderService;
import com.atp.product.utils.Constants;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import static com.atp.product.utils.Constants.*;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Implementation of ScheduledOrderService with optimized performance
 */
@Slf4j
@Service
public class ScheduledOrderServiceImpl implements ScheduledOrderService {

    private final MongoTemplate mongoTemplate;
    private final PurchaseOrderService purchaseOrderService;
    private final NotificationService notificationService;
    private final CommonServiceImpl commonService;
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;


    // Cache for authentication token - thread-safe
    private volatile String cachedToken;
    private volatile long tokenExpiryTime;
    private final Object tokenLock = new Object();

    @Value("${Karmak.partSearchURL}")
    private String karmaPartSearchURL;


    @Value("${Karmak.AccountNumber}")
    private String karmaAccountNumber;

    @Value("${Ocp-Apim-Subscription-Key}")
    private String subScriptionKey;

    // Third-party API configuration
    @Value("${third-party.api.url}")
    private String thirdPartyApiUrl;

    @Value("${third-party.api.login-url}")
    private String loginApiUrl;

    @Value("${third-party.api.username}")
    private String apiUsername;

    @Value("${third-party.api.password}")
    private String apiPassword;

    // Configuration properties for better maintainability
    @Value("${scheduled-order.max-frequency-days:365}")
    private int maxFrequencyDays;

    @Value("${scheduled-order.token-cache-duration-minutes:55}")
    private int tokenCacheDurationMinutes;

    @Value("${scheduled-order.api-timeout-seconds:30}")
    private int apiTimeoutSeconds;

    // MediaType constants
    private static final MediaType JSON = MediaType.get(CONTENT_TYPE_APPLICATION_JSON + "; charset=utf-8");

    @Autowired
    public ScheduledOrderServiceImpl(MongoTemplate mongoTemplate,
                                     PurchaseOrderService purchaseOrderService,
                                     NotificationService notificationService,
                                     CommonServiceImpl commonService) {
        this.mongoTemplate = mongoTemplate;
        this.purchaseOrderService = purchaseOrderService;
        this.notificationService = notificationService;
        this.commonService = commonService;

        // Initialize HTTP client with configurable timeout
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS) // Keep default for constructor
                .writeTimeout(30, java.util.concurrent.TimeUnit.SECONDS)   // Keep default for constructor
                .readTimeout(30, java.util.concurrent.TimeUnit.SECONDS)    // Keep default for constructor
                .build();

        this.objectMapper = new ObjectMapper();
    }

    @Override
    public ScheduledOrderResponse subscribe(ScheduledOrderRequest request) {
        // Input validation
        if (request == null) {
            throw new IllegalArgumentException("Scheduled order request cannot be null");
        }
        if (request.getFrequencyDays() != null && request.getFrequencyDays() > maxFrequencyDays) {
            throw new IllegalArgumentException("Frequency cannot exceed " + maxFrequencyDays + " days");
        }
        if (request.getFrequencyDays() != null && request.getFrequencyDays() < 1) {
            throw new IllegalArgumentException("Frequency must be at least 1 day");
        }

        log.info("Creating scheduled order subscription for customer: {}", request.getKarmakCustomerId());

        try {
            // Look for any scheduled order by original order ID
            Query query = new Query(Criteria.where(FIELD_ORIGINAL_ORDER_ID).is(request.getOriginalOrderId()));
            ScheduledOrder existingOrder = mongoTemplate.findOne(query, ScheduledOrder.class);

            if (existingOrder != null) {
                if (existingOrder.isActive()) {
                    // Already active
                    throw new ScheduledOrderAlreadyExistsException(
                            "A scheduled order already exists for original order ID: " + request.getOriginalOrderId()
                    );
                } else {
                    // Previously cancelled - reactivate
                    Update update = new Update()
                            .set(FIELD_ACTIVE, true)
                            .set(FIELD_STATUS, STATUS_ACTIVE)
                            .set(FIELD_FREQUENCY_DAYS, request.getFrequencyDays())
                            .set(FIELD_SUBSCRIPTION_DATE, LocalDate.now())
                            //.set("nextRunDate", LocalDate.now().plusDays(request.getFrequencyDays()))
                            .set(FIELD_NEXT_RUN_DATE, LocalDate.now())
                            .set(FIELD_EMAIL, request.getEmail())
                            .set(FIELD_NAME, request.getName())
                            .set(FIELD_NOTES, request.getNotes())
                            .set(FIELD_NOTIFY_ON_OUT_OF_STOCK, request.getNotifyOnOutOfStock())
                            .set(FIELD_UPDATED_AT, LocalDateTime.now());

                    mongoTemplate.updateFirst(query, update, ScheduledOrder.class);

                    ScheduledOrder reactivatedOrder = mongoTemplate.findOne(query, ScheduledOrder.class);

                    if (reactivatedOrder == null) {
                        throw new DomainException("Failed to reactivate scheduled order - order not found after update");
                    }
                    log.info("Reactivated existing scheduled order with ID: {}", reactivatedOrder.getId());
                    return mapToResponse(reactivatedOrder);
                }
            }

            // No existing subscription - create new one
            ScheduledOrder scheduledOrder = new ScheduledOrder();
            scheduledOrder.setKarmakCustomerId(request.getKarmakCustomerId());
            scheduledOrder.setCustomerCorrelationId(request.getCustomerCorrelationId());
            scheduledOrder.setOriginalOrderId(request.getOriginalOrderId());
            scheduledOrder.setEmail(request.getEmail());
            scheduledOrder.setName(request.getName());
            scheduledOrder.setFrequencyDays(request.getFrequencyDays());
            scheduledOrder.setSubscriptionDate(LocalDate.now());
            //scheduledOrder.setNextRunDate(LocalDate.now().plusDays(request.getFrequencyDays()));
            scheduledOrder.setNextRunDate(LocalDate.now());
            scheduledOrder.setNotes(request.getNotes());
            scheduledOrder.setNotifyOnOutOfStock(request.getNotifyOnOutOfStock());
            scheduledOrder.setActive(true);
            scheduledOrder.setStatus(STATUS_ACTIVE);
            scheduledOrder.setOrdersProcessed(0);
            scheduledOrder.setCreatedAt(LocalDateTime.now());
            scheduledOrder.setUpdatedAt(LocalDateTime.now());

            mongoTemplate.save(scheduledOrder);

            log.info("Created new scheduled order with ID: {}", scheduledOrder.getId());
            return mapToResponse(scheduledOrder);

        } catch (DomainException e) {
            log.error("Failed to create scheduled order for customer: {}", request.getKarmakCustomerId(), e);
            throw new DomainException("Failed to create scheduled order: " + e.getMessage(), e);
        }
    }

    public boolean doesScheduledOrderByOriginalOrderIdExist(String originalOrderId) {
        // Build a query to find documents where 'originalOrderId' matches the given ID
        Query query = new Query(Criteria.where(FIELD_ORIGINAL_ORDER_ID).is(originalOrderId));
        // This is more efficient than fetching the entire document if you only need to know if it exists.
        return mongoTemplate.exists(query, ScheduledOrder.class);
    }

    @Override
    public boolean cancel(String scheduledOrderId, String karmakCustomerId) {
        // Input validation
        if (scheduledOrderId == null || scheduledOrderId.trim().isEmpty()) {
            throw new IllegalArgumentException("Scheduled order ID cannot be null or empty");
        }
        if (karmakCustomerId == null || karmakCustomerId.trim().isEmpty()) {
            throw new IllegalArgumentException("Karmak customer ID cannot be null or empty");
        }

        log.info("Cancelling scheduled order: {} for customer: {}", scheduledOrderId, karmakCustomerId);

        try {
            Query query = new Query(Criteria.where(FIELD_ID).is(scheduledOrderId)
                    .and(FIELD_KARMAK_CUSTOMER_ID).is(karmakCustomerId)
                    .and(FIELD_ACTIVE).is(true));

            Update update = new Update()
                    .set(FIELD_ACTIVE, false)
                    .set(FIELD_STATUS, STATUS_CANCELLED)
                    .set(FIELD_UPDATED_AT, LocalDateTime.now())
                    .set(FIELD_NOTES, MSG_CANCELLED_BY_CUSTOMER);

            var result = mongoTemplate.updateFirst(query, update, ScheduledOrder.class);

            if (result.getModifiedCount() > 0) {
                // Send cancellation email
                //notificationService.sendScheduledOrderCancellation(customerId, scheduledOrderId);
                log.info("Scheduled order cancelled successfully: {}", scheduledOrderId);
                return true;
            } else {
                log.warn("Scheduled order not found or already cancelled: {}", scheduledOrderId);
                return false;
            }

        } catch (Exception e) {
            log.error("Failed to cancel scheduled order: {}", scheduledOrderId, e);
            return false;
        }
    }

    @Override
    public ScheduledOrderResponse updateSchedule(String scheduledOrderId, UpdateScheduleRequest request) {
        log.info("Updating scheduled order: {}", scheduledOrderId);

        try {
            Query query = new Query(Criteria.where(FIELD_ID).is(scheduledOrderId)
                    .and(FIELD_KARMAK_CUSTOMER_ID).is(request.getKarmakCustomerId())
                    .and(FIELD_ACTIVE).is(true));

            Update update = new Update().set(FIELD_UPDATED_AT, LocalDateTime.now());

            // Update only provided fields
            if (request.getFrequencyDays() != null) {
                update.set(FIELD_FREQUENCY_DAYS, request.getFrequencyDays());
            }
            if (request.getNextRunDate() != null) {
                update.set(FIELD_NEXT_RUN_DATE, request.getNextRunDate());
            }
            if (request.getNotes() != null) {
                update.set(FIELD_NOTES, request.getNotes());
            }
            // SECURITY: Do not allow customer ID changes to prevent authorization bypass
            // Customer ID should remain unchanged for security reasons
            if (request.getCustomerCorrelationId() != null) {
                update.set(FIELD_CUSTOMER_CORRELATION_ID, request.getCustomerCorrelationId());
            }
            if (request.getEmail() != null) {
                update.set(FIELD_EMAIL, request.getEmail());
            }
            if (request.getName() != null) {
                update.set(FIELD_NAME, request.getName());
            }
            if (request.getNotifyOnOutOfStock() != null) {
                update.set(FIELD_NOTIFY_ON_OUT_OF_STOCK, request.getNotifyOnOutOfStock());
            }
            if (request.getActive() != null) {
                update.set(FIELD_ACTIVE, request.getActive());
                update.set(FIELD_STATUS, Boolean.TRUE.equals(request.getActive()) ? STATUS_ACTIVE : STATUS_CANCELLED);
            }

            var result = mongoTemplate.updateFirst(query, update, ScheduledOrder.class);

            if (result.getModifiedCount() > 0) {
                // Get updated order
                ScheduledOrder updatedOrder = mongoTemplate.findOne(query, ScheduledOrder.class);

                // Send update notification
                if (request.getFrequencyDays() != null && updatedOrder != null) {
                    notificationService.sendScheduledOrderUpdate(
                            updatedOrder.getEmail(),
                            updatedOrder.getName(),
                            updatedOrder.getKarmakCustomerId(),
                            scheduledOrderId,
                            request.getFrequencyDays()
                    );
                }

                log.info("Scheduled order updated successfully: {}", scheduledOrderId);
                return mapToResponse(updatedOrder);
            } else {
                log.warn("Scheduled order not found for update: {}", scheduledOrderId);
                return null;
            }

        } catch (Exception e) {
            log.error("Failed to update scheduled order: {}", scheduledOrderId, e);
            throw new RuntimeException("Failed to update scheduled order: " + e.getMessage(), e);
        }
    }

    @Override
    public List<ScheduledOrderResponse> getScheduledOrdersByCustomer(String karmakCustomerId) {
        log.info("Retrieving scheduled orders for customer: {}", karmakCustomerId);

        try {
            Query query = new Query(Criteria.where(FIELD_KARMAK_CUSTOMER_ID).is(karmakCustomerId));
            List<ScheduledOrder> orders = mongoTemplate.find(query, ScheduledOrder.class);

            return orders.stream()
                    .map(this::mapToResponse)
                    .toList();

        } catch (Exception e) {
            log.error("Failed to retrieve scheduled orders for customer: {}", karmakCustomerId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public ScheduledOrderResponse getScheduledOrderById(String scheduledOrderId, String karmakCustomerId) {
        log.info("Retrieving scheduled order: {} for customer: {}", scheduledOrderId, karmakCustomerId);

        try {
            Query query = new Query(Criteria.where(FIELD_ID).is(scheduledOrderId)
                    .and(FIELD_KARMAK_CUSTOMER_ID).is(karmakCustomerId));

            ScheduledOrder order = mongoTemplate.findOne(query, ScheduledOrder.class);
            return order != null ? mapToResponse(order) : null;

        } catch (Exception e) {
            log.error("Failed to retrieve scheduled order: {} for customer: {}", scheduledOrderId, karmakCustomerId, e);
            return null;
        }
    }


    @Override
    public List<ScheduledOrderResponse> getAllActiveScheduledOrders() {
        log.info("Retrieving all active scheduled orders");

        try {
            Query query = new Query(Criteria.where(FIELD_ACTIVE).is(true));
            List<ScheduledOrder> orders = mongoTemplate.find(query, ScheduledOrder.class);

            return orders.stream()
                    .map(this::mapToResponse)
                    .toList();

        } catch (Exception e) {
            log.error("Failed to retrieve all active scheduled orders", e);
            return new ArrayList<>();
        }
    }

    private ScheduledOrderResponse mapToResponse(ScheduledOrder order) {
        ScheduledOrderResponse response = new ScheduledOrderResponse();
        response.setId(order.getId());
        response.setKarmakCustomerId(order.getKarmakCustomerId());
        response.setCustomerCorrelationId(order.getCustomerCorrelationId());
        response.setOriginalOrderId(order.getOriginalOrderId());
        response.setEmail(order.getEmail());
        response.setName(order.getName());
        response.setSubscriptionDate(order.getSubscriptionDate());
        response.setFrequencyDays(order.getFrequencyDays());
        response.setNextRunDate(order.getNextRunDate());
        response.setActive(order.isActive());
        response.setNotes(order.getNotes());
        response.setOrdersProcessed(order.getOrdersProcessed());
        response.setLastProcessedDate(order.getLastProcessedDate());
        response.setNotifyOnOutOfStock(order.getNotifyOnOutOfStock());
        response.setStatus(order.getStatus());
        response.setCreatedAt(order.getCreatedAt());
        response.setUpdatedAt(order.getUpdatedAt());
        return response;
    }

    /**
     * OPTIMIZED Pre-scheduler for handling large volumes with parallel processing
     * FIXED: Now properly compares available quantity vs ordered quantity to prevent partial fulfillment
     */
    //@Scheduled(cron = "0 0 6 * * ?") // Daily at 6:00 AM
    @Override
    //@Scheduled(fixedRate = 60000, initialDelay = 60000)
    @Scheduled(cron = "0 36 11 * * ?")
    public void checkAndUpdateOutOfStockOrders() {
        log.info("Daily Checking OUT_OF_STOCK 1 minutes orders for availability (OPTIMIZED)...");

        try {
            // OPTIMIZATION 1: Paginated processing to avoid memory issues
            int pageSize = DEFAULT_BATCH_SIZE; // Process in batches
            int pageNumber = 0;
            int totalProcessed = 0;

            // OPTIMIZATION 2: Global caching - thread-safe collections
            List<String> globalUnavailableParts = Collections.synchronizedList(new ArrayList<>());
            Map<String, Map<String, String>> globalAvailablePartsCache = new ConcurrentHashMap<>();
            List<ScheduledOrder> ordersToUpdate = Collections.synchronizedList(new ArrayList<>());

            boolean continueProcessing = true;
            while (continueProcessing) {
                // Get orders in batches
                Query query = new Query(Criteria.where(FIELD_ACTIVE).is(true)
                        .and(FIELD_STATUS).is(STATUS_OUT_OF_STOCK))
                        .skip((long)pageNumber * pageSize)
                        .limit(pageSize);

                List<ScheduledOrder> batchOrders = mongoTemplate.find(query, ScheduledOrder.class);

                if (batchOrders.isEmpty()) {
                    continueProcessing = false; // No more orders
                } else {
                    log.info("Processing batch {} with {} orders", pageNumber + 1, batchOrders.size());

                    // OPTIMIZATION 3: Parallel processing of batch
                    boolean batchSuccess = processBatchOrders(batchOrders, globalUnavailableParts, globalAvailablePartsCache, ordersToUpdate);

                    if (!batchSuccess) {
                        continueProcessing = false; // Exit if interrupted
                    } else {
                        totalProcessed += batchOrders.size();
                        pageNumber++;

                        log.info("Completed batch {}. Total processed: {}", pageNumber, totalProcessed);

                        // Optional: Add small delay between batches to avoid overwhelming APIs
                        continueProcessing = waitBetweenBatches();
                    }
                }
            }

            // OPTIMIZATION 4: Batch database updates
            if (!ordersToUpdate.isEmpty()) {
                batchUpdateOrdersToActive(ordersToUpdate);
            }

            log.info("Daily pre-scheduler completed: {} orders processed, {} made available",
                    totalProcessed, ordersToUpdate.size());
            log.info("Global cache: {} unavailable parts, {} available parts cached",
                    globalUnavailableParts.size(), globalAvailablePartsCache.size());

            // FIXED: Clear caches to prevent memory leaks
            globalUnavailableParts.clear();
            globalAvailablePartsCache.clear();
            log.debug("Cleared global caches to prevent memory leaks");

        } catch (Exception e) {
            log.error("Error in optimized daily pre-scheduler", e);
        }
    }

    /**
     * Process a batch of orders with parallel execution (extracted to fix SonarQube nested try block issue)
     * Logic unchanged - same as original nested try block
     */
    private boolean processBatchOrders(List<ScheduledOrder> batchOrders,
                                       List<String> globalUnavailableParts,
                                       Map<String, Map<String, String>> globalAvailablePartsCache,
                                       List<ScheduledOrder> ordersToUpdate) {
        List<CompletableFuture<Void>> futures = batchOrders.stream()
                .map(order -> processOrderAsync(order, globalUnavailableParts, globalAvailablePartsCache, ordersToUpdate))
                .toList();

        // Wait for batch to complete
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            return true;
        } catch (CompletionException e) {
            if (e.getCause() instanceof InterruptedException) {
                Thread.currentThread().interrupt();
                log.warn("Pre-scheduler interrupted during batch processing", e);
                return false; // Exit the loop gracefully
            }
            throw e; // Re-throw other completion exceptions
        }
    }

    /**
     * Wait between batches - extracted to fix SonarQube Thread.sleep issue
     * Logic unchanged - same as original try-catch block
     */
    @SuppressWarnings("java:S2142") // SonarQube: Thread.sleep in loop is intentional here for rate limiting
    private boolean waitBetweenBatches() {
        try {
            Thread.sleep(DEFAULT_BATCH_DELAY_MS);
            return true;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // Re-interrupt the thread
            log.warn("Pre-scheduler interrupted during batch delay", e);
            return false; // Exit the loop gracefully
        }
    }

    /**
     * OPTIMIZATION 3: Async processing of individual orders
     */
    private CompletableFuture<Void> processOrderAsync(ScheduledOrder scheduledOrder,
                                                      List<String> globalUnavailableParts,
                                                      Map<String, Map<String, String>> globalAvailablePartsCache,
                                                      List<ScheduledOrder> ordersToUpdate) {
        return CompletableFuture.runAsync(() -> {
            try {
                if (checkIfPartsNowAvailableOptimized(scheduledOrder, globalUnavailableParts, globalAvailablePartsCache)) {
                    ordersToUpdate.add(scheduledOrder);
                    log.info("Order {} marked for update to ACTIVE", scheduledOrder.getId());
                }
            } catch (Exception e) {
                log.error("Error processing order {} in parallel: {}", scheduledOrder.getId(), e.getMessage());
            }
        });
    }

    /**
     * OPTIMIZED parts checking with parallel API calls and better caching
     */
    private boolean checkIfPartsNowAvailableOptimized(ScheduledOrder scheduledOrder,
                                                      List<String> globalUnavailableParts,
                                                      Map<String, Map<String, String>> globalAvailablePartsCache) {
        log.info("Checking parts for order: {}", scheduledOrder.getId());

        try {
            List<PurchaseOrder> purchaseOrders = getPurchaseOrdersForScheduledOrder(scheduledOrder);
            if (purchaseOrders == null || purchaseOrders.isEmpty()) {
                return false;
            }

            PartCategorizationResult categorization = categorizePartsByAvailability(
                    purchaseOrders, globalUnavailableParts, globalAvailablePartsCache);

            if (categorization.hasCachedUnavailableParts()) {
                log.info("Order {} has cached unavailable parts: {}",
                        scheduledOrder.getId(), categorization.getCachedUnavailableParts());
                return false;
            }

            boolean allPartsAvailable = processPartsNeedingApiCalls(
                    categorization.getPartsNeedingApiCall(),
                    scheduledOrder,
                    globalUnavailableParts,
                    globalAvailablePartsCache);

            if (allPartsAvailable) {
                log.info("All parts available for order: {}", scheduledOrder.getId());
            }
            return allPartsAvailable;

        } catch (Exception e) {
            log.error("Error checking parts for order {}: {}", scheduledOrder.getId(), e.getMessage());
            return false;
        }
    }

    private List<PurchaseOrder> getPurchaseOrdersForScheduledOrder(ScheduledOrder scheduledOrder) {
        AtommPurchaseOrder purchaseOrder = purchaseOrderService.getPurchaseOrderByExternalId(
                scheduledOrder.getOriginalOrderId());

        if (purchaseOrder == null || purchaseOrder.getComplexValues() == null
                || purchaseOrder.getComplexValues().getPurchaseOrders() == null) {
            log.warn("No purchase order data found for order: {}", scheduledOrder.getId());
            return Collections.emptyList(); // FIXED: Return empty list instead of null
        }

        return purchaseOrder.getComplexValues().getPurchaseOrders();
    }

    private PartCategorizationResult categorizePartsByAvailability(List<PurchaseOrder> purchaseOrders,
                                                                   List<String> globalUnavailableParts,
                                                                   Map<String, Map<String, String>> globalAvailablePartsCache) {
        List<PartQuantityInfo> partsNeedingApiCall = new ArrayList<>();
        List<String> cachedUnavailableParts = new ArrayList<>();

        for (PurchaseOrder poItem : purchaseOrders) {
            String partNumber = extractPartNumber(poItem.getValues());
            if (PART_NUMBER_NOT_AVAILABLE.equals(partNumber)) {
                continue;
            }

            if (globalUnavailableParts.contains(partNumber)) {
                cachedUnavailableParts.add(partNumber);
            } else if (!globalAvailablePartsCache.containsKey(partNumber)) {
                int orderedQuantity;
                try {
                    orderedQuantity = Integer.parseInt(extractQuantity(poItem.getValues()));
                } catch (NumberFormatException e) {
                    log.warn("Invalid quantity format for part {}, defaulting to 0", partNumber);
                    orderedQuantity = 0;
                }

                if (orderedQuantity <= 0) {
                    log.info("Skipping part {} because ordered quantity is {}", partNumber, orderedQuantity);
                    continue;
                }

                partsNeedingApiCall.add(new PartQuantityInfo(partNumber, orderedQuantity));
            }
        }

        return new PartCategorizationResult(partsNeedingApiCall, cachedUnavailableParts);
    }

    private boolean processPartsNeedingApiCalls(List<PartQuantityInfo> partsNeedingApiCall,
                                                ScheduledOrder scheduledOrder,
                                                List<String> globalUnavailableParts,
                                                Map<String, Map<String, String>> globalAvailablePartsCache) {
        if (partsNeedingApiCall.isEmpty()) {
            return true;
        }

        log.info("Making {} parallel API calls for order {} with quantities: {}",
                partsNeedingApiCall.size(), scheduledOrder.getId(),
                partsNeedingApiCall.stream()
                        .map(p -> p.getPartNumber() + ":" + p.getOrderedQuantity())
                        .collect(Collectors.joining(", ")));

        List<PartAvailabilityResult> results = executeParallelApiCalls(partsNeedingApiCall, scheduledOrder);
        return processApiCallResults(results, globalUnavailableParts, globalAvailablePartsCache);
    }

    private List<PartAvailabilityResult> executeParallelApiCalls(List<PartQuantityInfo> partsNeedingApiCall,
                                                                 ScheduledOrder scheduledOrder) {
        List<CompletableFuture<PartAvailabilityResult>> apiCalls = partsNeedingApiCall.stream()
                .map(partInfo -> checkPartAvailabilityAsync(partInfo.getPartNumber(),
                        scheduledOrder.getKarmakCustomerId(),
                        partInfo.getOrderedQuantity()))
                .toList();

        return apiCalls.stream()
                .map(CompletableFuture::join)
                .toList();
    }

    private boolean processApiCallResults(List<PartAvailabilityResult> results,
                                          List<String> globalUnavailableParts,
                                          Map<String, Map<String, String>> globalAvailablePartsCache) {
        for (PartAvailabilityResult result : results) {
            if (result.isAvailable()) {
                globalAvailablePartsCache.put(result.getPartNumber(), result.getApiResult());
                log.info("Part {} cached as available", result.getPartNumber());
            } else {
                globalUnavailableParts.add(result.getPartNumber());
                log.info("Part {} cached as unavailable", result.getPartNumber());
                return false;
            }
        }
        return true;
    }

    /**
     * Helper class to hold part categorization results
     */
    private static class PartCategorizationResult {
        private final List<PartQuantityInfo> partsNeedingApiCall;
        private final List<String> cachedUnavailableParts;

        public PartCategorizationResult(List<PartQuantityInfo> partsNeedingApiCall, List<String> cachedUnavailableParts) {
            this.partsNeedingApiCall = partsNeedingApiCall;
            this.cachedUnavailableParts = cachedUnavailableParts;
        }

        public List<PartQuantityInfo> getPartsNeedingApiCall() {
            return partsNeedingApiCall;
        }

        public List<String> getCachedUnavailableParts() {
            return cachedUnavailableParts;
        }

        public boolean hasCachedUnavailableParts() {
            return !cachedUnavailableParts.isEmpty();
        }
    }

    /**
     * OPTIMIZATION 6: Async API call wrapper with quantity comparison
     */
    private CompletableFuture<PartAvailabilityResult> checkPartAvailabilityAsync(String partNumber, String customerId, int orderedQuantity) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("Making async API call for part: {} (ordered: {})", partNumber, orderedQuantity);
            /* Map<String, String> result = getPriceAndQuantityForProductAsync(partNumber, customerId).join();
            String availableQty = result.get(Constants.QUANTITY); */

                Map<String, String> result = new HashMap<>();
                String availableQty = "10";

                // FIXED: Compare available quantity against ordered quantity
                boolean isAvailable = availableQty != null &&
                        Integer.parseInt(availableQty) >= orderedQuantity;

                if (availableQty != null) {
                    log.info("Part {} - Available: {}, Ordered: {}, Sufficient: {}",
                            partNumber, availableQty, orderedQuantity, isAvailable);
                }

                return new PartAvailabilityResult(partNumber, isAvailable, result);

            } catch (CompletionException e) {
                if (e.getCause() instanceof InterruptedException) {
                    Thread.currentThread().interrupt();
                    log.warn("API call interrupted for part: {}", partNumber, e);
                    return new PartAvailabilityResult(partNumber, false, null);
                }
                log.error("API call failed for part {}: {}", partNumber, e.getMessage());
                return new PartAvailabilityResult(partNumber, false, null);
            } catch (Exception e) {
                log.error("API call failed for part {}: {}", partNumber, e.getMessage());
                return new PartAvailabilityResult(partNumber, false, null);
            }
        });
    }

    /**
     * OPTIMIZATION 4: Batch database updates
     */
    private void batchUpdateOrdersToActive(List<ScheduledOrder> ordersToUpdate) {
        log.info("Batch updating {} orders to ACTIVE status", ordersToUpdate.size());

        try {
            LocalDate today = LocalDate.now();
            List<String> orderIds = ordersToUpdate.stream()
                    .map(ScheduledOrder::getId)
                    .toList();

            // Single bulk update query
            Query query = new Query(Criteria.where(FIELD_ID).in(orderIds));
            Update update = new Update()
                    .set(FIELD_STATUS, STATUS_ACTIVE)
                    .set(FIELD_NEXT_RUN_DATE, today)
                    .set(FIELD_NOTES, MSG_PARTS_RESTOCKED)
                    .set(FIELD_UPDATED_AT, LocalDateTime.now());

            UpdateResult result = mongoTemplate.updateMulti(query, update, ScheduledOrder.class);

            log.info("Batch update completed: {} orders updated", result.getModifiedCount());

            // // Send notifications in parallel
            // List<CompletableFuture<Void>> notificationFutures = ordersToUpdate.stream()
            //     .map(this::sendRestockNotificationAsync)
            //     .collect(Collectors.toList());

            // CompletableFuture.allOf(notificationFutures.toArray(new CompletableFuture[0])).join();

        } catch (Exception e) {
            log.error("Error in batch update", e);
        }
    }

// /**
//  * Async notification sending
//  */
// private CompletableFuture<Void> sendRestockNotificationAsync(ScheduledOrder order) {
//     return CompletableFuture.runAsync(() -> {
//         try {
//             notificationService.sendPartsRestockedNotification(
//                 order.getEmail(), order.getName(),
//                 order.getKarmakCustomerId(), order.getId());
//         } catch (Exception e) {
//             log.error("Failed to send notification for order {}: {}", order.getId(), e.getMessage());
//         }
//     });
// }


    /**
     * Optimized scheduled order processing (moved from CommonServiceImpl)
     * FIXED: Now properly validates quantities to prevent partial order fulfillment
     */
    @Override
    @Scheduled(cron = "0 34 11 * * ?")
    //@Scheduled(cron = "0 0 8 * * ?") // Daily at 8:00 AM (2 hours after pre-scheduler)
    public void processScheduledOrders() {
        log.info("Starting Method  processScheduledOrders 2 minutes scheduled order processing...");

        LocalDate today = LocalDate.now();
        // Simple query - get orders due today (including ones updated by pre-scheduler)
        Query query = new Query(Criteria.where(FIELD_ACTIVE).is(true)
                .and(FIELD_NEXT_RUN_DATE).is(today)
                .and(FIELD_STATUS).is(STATUS_ACTIVE)); // Only ACTIVE orders

        List<ScheduledOrder> dueOrders = mongoTemplate.find(query, ScheduledOrder.class);
        log.info("Found {} scheduled orders due for processing", dueOrders.size());

        if (dueOrders.isEmpty()) {
            log.info("No scheduled orders due for processing");
            return;
        }

        for (ScheduledOrder order : dueOrders) {
            processSingleScheduledOrder(order, new ArrayList<>());
        }

       /* // FIXED: Add global unavailable items tracking like original (thread-safe for parallel processing)
        List<String> globalUnavailableItems = Collections.synchronizedList(new ArrayList<>());

        // Process orders in parallel for better performance
        List<CompletableFuture<Void>> futures = dueOrders.stream()
                .map(order -> processScheduledOrderAsync(order, globalUnavailableItems))
                .toList();

        // Wait for all orders to complete processing
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenRun(() -> log.info("Completed processing {} scheduled orders", dueOrders.size()))
                .exceptionally(throwable -> {
                    log.error("Error during batch processing of scheduled orders", throwable);
                    return null;
                });*/
    }

    /**
     * Process a single scheduled order asynchronously
     */
    private CompletableFuture<Void> processScheduledOrderAsync(ScheduledOrder scheduledOrder,
                                                               List<String> globalUnavailableItems) {
        return CompletableFuture.runAsync(() -> {
            try {
                processSingleScheduledOrder(scheduledOrder, globalUnavailableItems);
            } catch (Exception e) {
                log.error("Failed to process scheduled order: {}", scheduledOrder.getId(), e);

                // Send failure notification
                notificationService.sendPurchaseOrderFailure(
                        scheduledOrder.getEmail(),
                        scheduledOrder.getName(),
                        scheduledOrder.getKarmakCustomerId(),
                        scheduledOrder.getId(),
                        e.getMessage()
                );
            }
        });
    }

    /**
     * Process a single scheduled order
     * FIXED: Now properly compares available quantity vs ordered quantity to prevent partial fulfillment
     */
    private void processSingleScheduledOrder(ScheduledOrder scheduledOrder,
                                             List<String> globalUnavailableItems) {
        log.info("Processing scheduled order: {} for customer: {}",
                scheduledOrder.getId(), scheduledOrder.getKarmakCustomerId());

        // Get the original purchase order
        AtommPurchaseOrder purchaseOrder = purchaseOrderService.getPurchaseOrderByExternalId(
                scheduledOrder.getOriginalOrderId());

        if (purchaseOrder == null) {
            log.warn("No purchase order found for external ID {}", scheduledOrder.getOriginalOrderId());
            return;
        }

        // Extract and validate complex values
        ComplexValues complexValuesContainer = purchaseOrder.getComplexValues();
        if (complexValuesContainer == null || complexValuesContainer.getPurchaseOrders() == null) {
            log.warn("No complex values found in purchase order {}", scheduledOrder.getOriginalOrderId());
            return;
        }

        List<PurchaseOrder> purchaseOrders = complexValuesContainer.getPurchaseOrders();
        List<String> unavailableParts = new ArrayList<>();
        List<InternalPurchaseOrderRequest.InternalLineItem> internalLineItems = new ArrayList<>();

        // Process each purchase order item
        for (PurchaseOrder poItem : purchaseOrders) {
            Values values = poItem.getValues();
            if (values == null) {
                log.warn("Skipping purchase order item with null values.");
                continue;
            }

            String partNumber = extractPartNumber(values);
            if (PART_NUMBER_NOT_AVAILABLE.equals(partNumber)) {
                log.warn("Invalid part number for order {}, skipping", scheduledOrder.getId());
                continue;
            }

            String orderedQuantityStr = extractQuantity(values);
            int orderedQuantity;
            try {
                orderedQuantity = Integer.parseInt(orderedQuantityStr);
            } catch (NumberFormatException e) {
                log.warn("Invalid ordered quantity for part {}: {}, defaulting to 0", partNumber, orderedQuantityStr);
                orderedQuantity = 0;
            }

            // 🚫 Skip if quantity is zero
            if (orderedQuantity <= 0) {
                log.info("Skipping part {} in order {} because ordered quantity is {}",
                        partNumber, scheduledOrder.getId(), orderedQuantity);
                continue;
            }

            // Check global unavailable items first
            if (globalUnavailableItems.contains(partNumber)) {
                unavailableParts.add(partNumber);
                continue;
            }

            // Check availability and pricing
            try {
                /* CompletableFuture<Map<String, String>> resultFuture = getPriceAndQuantityForProductAsync(partNumber, scheduledOrder.getKarmakCustomerId());
                Map<String, String> result = resultFuture.join();

                String availableQty = result.get(Constants.QUANTITY);
                String price = result.get(Constants.PRICE);
                String branch = result.get(Constants.BRANCH_CODE);
                String karmakPartId = result.get(Constants.PARTS_INVENTORY_DETAIL_ID); */

                String availableQty = "9";
                String price = "100";
                String branch = "1";
                String karmakPartId = "1";

                // FIXED: Compare available quantity against ordered quantity
                if (Integer.parseInt(availableQty) < orderedQuantity) {
                    log.info("Part {} insufficient stock - Available: {}, Ordered: {}",
                            partNumber, availableQty, orderedQuantity);
                    unavailableParts.add(partNumber);
                    globalUnavailableItems.add(partNumber);
                    continue;
                }

                log.info("Part {} sufficient stock - Available: {}, Ordered: {}",
                        partNumber, availableQty, orderedQuantity);

                // Create line item with proper quantities
                InternalPurchaseOrderRequest.InternalLineItem lineItem =
                        new InternalPurchaseOrderRequest.InternalLineItem();
                lineItem.setPartID(Integer.parseInt(karmakPartId));
                lineItem.setQuantity(orderedQuantity); // Use ordered quantity consistently
                lineItem.setCost(Double.parseDouble(price));
                lineItem.setMessage(MSG_SCHEDULED_ORDER);
                lineItem.setBranch(branch);
                lineItem.setTotalQuantity(Integer.parseInt(availableQty)); // Use ordered quantity consistently

                internalLineItems.add(lineItem);

            } catch (Exception e) {
                log.error("Error checking availability for part {} in order {}",
                        partNumber, scheduledOrder.getId(), e);
                unavailableParts.add(partNumber);
            }
        }

        // Process the order if all parts are available
        if (unavailableParts.isEmpty() && !internalLineItems.isEmpty()) {
            processAvailableOrder(scheduledOrder, internalLineItems, purchaseOrder);
        } else {
            handleUnavailableOrder(scheduledOrder, unavailableParts);
        }
    }

    public CompletableFuture<Map<String, String>> getPriceAndQuantityForProductAsync(String partNumber, String karmakCustomerId) {
        try {
            String customerID = getCustomerIdOrDefault(karmakCustomerId);
            String requestJson = buildPartSearchRequestJson(partNumber, customerID);
            Response response = executePartSearchRequest(requestJson);

            Map<String, String> results = parsePartSearchResponse(response);
            return CompletableFuture.completedFuture(results);

        } catch (Exception e) {
            throw new DomainException("An error occurred while calling karmak api", e.getMessage());
        }
    }

    private String getCustomerIdOrDefault(String karmakCustomerId) {
        if (karmakCustomerId != null && !karmakCustomerId.isEmpty()) {
            return karmakCustomerId;
        }
        return NULL_STRING;
    }

    private String buildPartSearchRequestJson(String partNumber, String customerID) {
        log.info("int customer ID " + customerID);
        return "{\"" + JSON_CUSTOMER_ID + "\": " + customerID + "," + "\"" + JSON_LOCATION_ID + "\": 1," + "\"" + JSON_REGION + "\": null,"
                + "\"" + JSON_PARTS + "\": [{" + "\"" + JSON_NUMBER + "\": \"" + partNumber + "\"," + "\"" + JSON_DESCRIPTION + "\": null,"
                + "\"" + JSON_EXACT_MATCH + "\": true," + "\"" + JSON_SOURCE + "\": null," + "\"" + JSON_CROSS_REFERENCE + "\": false," + "\"" + JSON_PAGE_SIZE + "\": 1" + "}]" + "}";
    }

    private Response executePartSearchRequest(String requestJson) throws KarmakApiException {
        try {
            RequestBody body = RequestBody.create(requestJson, MediaType.parse(CONTENT_TYPE_APPLICATION_JSON));
            Request partRequest = new Request.Builder().url(karmaPartSearchURL)
                    .addHeader(HEADER_CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                    .addHeader(HEADER_CACHE_CONTROL, CACHE_CONTROL_NO_CACHE)
                    .addHeader(HEADER_KARMAK_ACCOUNT_NUMBER, karmaAccountNumber)
                    .addHeader(HEADER_OCP_APIM_SUBSCRIPTION_KEY, subScriptionKey)
                    .post(body).build();

            Call call = httpClient.newCall(partRequest);
            return call.execute();
        } catch (IOException e) {
            throw new KarmakApiException("Failed to execute part search request to Karmak API", e);
        }
    }

    private Map<String, String> parsePartSearchResponse(Response response) throws KarmakApiException {
        try {
            Map<String, String> finalResults = new HashMap<>();

            if (response.code() < 200 || response.code() >= 300) {
                throw new KarmakApiException("Karmak API returned error status: " + response.code());
            }

            // FIXED: Add null check for response body
            ResponseBody responseBody = response.body();
            if (responseBody == null) {
                throw new KarmakApiException("Karmak API response body is null");
            }

            JsonNode json = objectMapper.readValue(responseBody.string(), JsonNode.class);
            response.close();

            JsonNode firstPart = extractFirstPartFromResponse(json);
            if (firstPart == null) {
                return finalResults;
            }

            extractPartDetailsToResults(firstPart, finalResults);
            return finalResults;

        } catch (IOException e) {
            throw new KarmakApiException("Failed to parse Karmak API response", e);
        } catch (Exception e) {
            throw new KarmakApiException("Unexpected error while processing Karmak API response", e);
        }
    }

    private JsonNode extractFirstPartFromResponse(JsonNode json) {
        if (!(json instanceof ArrayNode results)) {
            return null;
        }

        if (results.isEmpty()) {
            return null;
        }

        JsonNode entries = results.get(0).get("results");
        if (entries == null) {
            return null;
        }

        ArrayNode parts = (ArrayNode) entries;
        return parts.get(0);
    }

    private void extractPartDetailsToResults(JsonNode firstPart, Map<String, String> finalResults) {
        JsonNode priceNode = firstPart.get("price");
        JsonNode availableNode = firstPart.get("available");
        JsonNode inventoryDetailIdNode = firstPart.get("PartsInventoryDetailID");
        JsonNode branchCodeNode = firstPart.get("branchCode");

        if (priceNode != null && availableNode != null) {
            finalResults.put(Constants.PRICE, priceNode.get("customerPrice").asText());
            finalResults.put(Constants.QUANTITY, availableNode.asText());
            finalResults.put(Constants.PARTS_INVENTORY_DETAIL_ID, Integer.toString(inventoryDetailIdNode.asInt()));
            finalResults.put(Constants.BRANCH_CODE, branchCodeNode.asText());
        } else {
            log.info("Price or available field is missing in the first element.");
        }
    }

    /**
     * Process order when all parts are available
     */
    private void processAvailableOrder(ScheduledOrder scheduledOrder,
                                       List<InternalPurchaseOrderRequest.InternalLineItem> lineItems,
                                       AtommPurchaseOrder originalOrder) {
        try {
            // FIXED: Create internal purchase order request like original code
            InternalPurchaseOrderRequest internalRequest = new InternalPurchaseOrderRequest();
            internalRequest.setPoDate(LocalDate.now());
            internalRequest.setLineItems(lineItems);
            internalRequest.setSupplierID(0); // FIXED: set appropriately like original
            internalRequest.setApVendorID(0); // FIXED: set appropriately like original
            internalRequest.setLocationID(1); // set appropriately
            internalRequest.setFillingLocationID(1); // set appropriately
            internalRequest.setOrderedBy(SYSTEM_USER_SCHEDULER); // FIXED: like original
            internalRequest.setDirectShip(false);


            // Create purchase order
            /*PurchaseOrderResponse response = commonService.createPurchaseOrder(internalRequest);

            if (response != null) {
                log.info("PO created successfully: {} for scheduled order: {}",
                        response.getPartsPurchaseOrder().getPoNumber(), scheduledOrder.getId());

                // Extract PO information from response
                String partsPurchaseOrderID = String.valueOf(response.getPartsPurchaseOrder().getPartsPurchaseOrderID());
                String poNumber = response.getPartsPurchaseOrder().getPoNumber();

                // Update originalOrder Values with PO information
                updateOriginalOrderWithPOInfo(originalOrder, partsPurchaseOrderID, poNumber);

                // Place the order (external API call)
                boolean orderPlaced = placeOrder(originalOrder);
                if (!orderPlaced) {
                    log.warn("Failed to place order via external API for scheduled order: {}",
                            scheduledOrder.getId());
                    // Continue with processing even if external API fails
                }

                // Update scheduled order for next run
                updateScheduledOrderAfterSuccess(scheduledOrder);

                // Send success notification
                notificationService.sendPurchaseOrderSuccess(
                    scheduledOrder.getEmail(),
                    scheduledOrder.getName(),
                    scheduledOrder.getKarmakCustomerId(),
                    response.getPartsPurchaseOrder().getPoNumber(),
                    scheduledOrder.getId()
                );

            } else {
                log.error("PO creation failed for scheduled order {}", scheduledOrder.getId());

                // Send failure notification
                notificationService.sendPurchaseOrderFailure(
                    scheduledOrder.getEmail(),
                    scheduledOrder.getName(),
                    scheduledOrder.getKarmakCustomerId(),
                    scheduledOrder.getId(),
                    "Purchase order creation failed"
                );
            }*/

            /*String partsPurchaseOrderID = "12345";
            String poNumber = "67890";

            // Update originalOrder Values with PO information
            updateOriginalOrderWithPOInfo(originalOrder, partsPurchaseOrderID, poNumber);
            log.info("Original order updated with PO info: {}", originalOrder);


            //boolean orderPlaced = placeOrder(originalOrder);*/

            PurchaseOrderResponse response= new PurchaseOrderResponse();
            PurchaseOrderResponse.PartsPurchaseOrder partsPurchaseOrder = new PurchaseOrderResponse.PartsPurchaseOrder();

            partsPurchaseOrder.setPartsPurchaseOrderID(12345);
            partsPurchaseOrder.setPoNumber("67890");
            partsPurchaseOrder.setPoTotal(100.0);

            PurchaseOrderResponse.LineItem lineItem = new PurchaseOrderResponse.LineItem();
            lineItem.setPartNumber("10");
            lineItem.setQuantity(1);
            lineItem.setCost(100.0);

            partsPurchaseOrder.setLineItems(Collections.singletonList(lineItem));

            response.setPartsPurchaseOrder(partsPurchaseOrder);


        } catch (Exception e) {
            log.error("Exception while processing available order {}: {}",
                    scheduledOrder.getId(), e.getMessage(), e);

            // Send failure notification
            notificationService.sendPurchaseOrderFailure(
                    scheduledOrder.getEmail(),
                    scheduledOrder.getName(),
                    scheduledOrder.getKarmakCustomerId(),
                    scheduledOrder.getId(),
                    e.getMessage()
            );
        }
    }

    /**
     * Handle order when some parts are unavailable - set to OUT_OF_STOCK
     * Uses existing fields only - no model changes needed
     */
    private void handleUnavailableOrder(ScheduledOrder scheduledOrder, List<String> unavailableParts) {
        log.info("Setting order {} to OUT_OF_STOCK due to unavailable parts: {}",
                scheduledOrder.getId(), unavailableParts);

        try {
            Query query = new Query(Criteria.where(FIELD_ID).is(scheduledOrder.getId()));

            Update update = new Update()
                    .set(FIELD_STATUS, STATUS_OUT_OF_STOCK)  // Use existing status field
                    .set(FIELD_NOTES, MSG_OUT_OF_STOCK_PREFIX + String.join(", ", unavailableParts))  // Use existing notes field
                    .set(FIELD_UPDATED_AT, LocalDateTime.now());

            mongoTemplate.updateFirst(query, update, ScheduledOrder.class);

            log.info("Updated scheduled order {} status to OUT_OF_STOCK", scheduledOrder.getId());

            // Send out of stock notification if enabled
            if (Boolean.TRUE.equals(scheduledOrder.getNotifyOnOutOfStock())) {
                notificationService.sendOutOfStockEmail(scheduledOrder, unavailableParts);
            }

            log.info("Order {} marked as OUT_OF_STOCK, will be checked tomorrow by pre-scheduler", scheduledOrder.getId());

        } catch (Exception e) {
            log.error("Error updating scheduled order {} to OUT_OF_STOCK: {}",
                    scheduledOrder.getId(), e.getMessage(), e);
        }
    }


    /**
     * Helper class for API results
     */
    private static class PartAvailabilityResult {
        private final String partNumber;
        private final boolean available;
        private final Map<String, String> apiResult;

        public PartAvailabilityResult(String partNumber, boolean available, Map<String, String> apiResult) {
            this.partNumber = partNumber;
            this.available = available;
            this.apiResult = apiResult;
        }

        public String getPartNumber() {
            return partNumber;
        }

        public boolean isAvailable() {
            return available;
        }

        public Map<String, String> getApiResult() {
            return apiResult;
        }
    }

    /**
     * Helper class to hold part number and ordered quantity information
     */
    private static class PartQuantityInfo {
        private final String partNumber;
        private final int orderedQuantity;

        public PartQuantityInfo(String partNumber, int orderedQuantity) {
            this.partNumber = partNumber;
            this.orderedQuantity = orderedQuantity;
        }

        public String getPartNumber() {
            return partNumber;
        }

        public int getOrderedQuantity() {
            return orderedQuantity;
        }
    }


    /**
     * Update scheduled order after successful processing
     */
    private void updateScheduledOrderAfterSuccess(ScheduledOrder scheduledOrder) {
        Query query = new Query(Criteria.where(FIELD_ID).is(scheduledOrder.getId()));

        boolean wasOutOfStock = STATUS_OUT_OF_STOCK.equals(scheduledOrder.getStatus());

        Update update = new Update()
                .set(FIELD_NEXT_RUN_DATE, scheduledOrder.getNextRunDate().plusDays(scheduledOrder.getFrequencyDays()))
                .set(FIELD_ORDERS_PROCESSED, scheduledOrder.getOrdersProcessed() + 1)
                .set(FIELD_LAST_PROCESSED_DATE, LocalDateTime.now())
                .set(FIELD_STATUS, STATUS_ACTIVE)  // Reset status back to ACTIVE from OUT_OF_STOCK
                .set(FIELD_NOTES, wasOutOfStock ? MSG_PROCESSED_AFTER_RESTOCK : MSG_PROCESSED_SUCCESSFULLY)
                .set(FIELD_UPDATED_AT, LocalDateTime.now());

        mongoTemplate.updateFirst(query, update, ScheduledOrder.class);

        log.info("Updated scheduled order {} for next run on {} (was OUT_OF_STOCK: {})",
                scheduledOrder.getId(),
                scheduledOrder.getNextRunDate().plusDays(scheduledOrder.getFrequencyDays()),
                wasOutOfStock);
    }

    /**
     * Update scheduled order next run date
     */
    private void updateScheduledOrderNextRun(ScheduledOrder scheduledOrder, LocalDate nextRunDate) {
        Query query = new Query(Criteria.where(FIELD_ID).is(scheduledOrder.getId()));

        Update update = new Update()
                .set(FIELD_NEXT_RUN_DATE, nextRunDate)
                .set(FIELD_UPDATED_AT, LocalDateTime.now());

        mongoTemplate.updateFirst(query, update, ScheduledOrder.class);
    }

    /**
     * Update originalOrder Values with PO information from createPurchaseOrder response
     *
     * @param originalOrder        The original order to update
     * @param partsPurchaseOrderID The Parts Purchase Order ID from response
     * @param poNumber             The PO Number from response
     */
    /*private void updateOriginalOrderWithPOInfo(AtommPurchaseOrder originalOrder,
                                               String partsPurchaseOrderID,
                                               String poNumber) {
        try {
            log.debug("Updating originalOrder Values with PO info - PartsPurchaseOrderID: {}, PONumber: {}",
                    partsPurchaseOrderID, poNumber);

            // Get existing Values list or create new one
            List<com.atp.product.controller.dto.response.atomm_responses.Value> values = originalOrder.getValues();
            if (values == null) {
                values = new ArrayList<>();
                originalOrder.setValues(values);
            }

            // Update or add PartsPurchaseOrderID
            updateOrAddValueField(values, "PartsPurchaseOrderID", partsPurchaseOrderID);

            // Update or add PONumber
            updateOrAddValueField(values, "PONumber", poNumber);

            log.info("Successfully updated originalOrder Values with PartsPurchaseOrderID: {} and PONumber: {}",
                    partsPurchaseOrderID, poNumber);

        } catch (Exception e) {
            log.error("Failed to update originalOrder with PO info - PartsPurchaseOrderID: {}, PONumber: {}",
                    partsPurchaseOrderID, poNumber, e);
        }
    }

    *//**
     * Update or add a specific field in the Values array
     *
     * @param values      The Values list to update
     * @param attributeId The attribute ID to update/add
     * @param value       The value to set
     *//*
    private void updateOrAddValueField(List<com.atp.product.controller.dto.response.atomm_responses.Value> values,
                                       String attributeId,
                                       String value) {
        // Look for existing field
        com.atp.product.controller.dto.response.atomm_responses.Value existingValue = values.stream()
                .filter(v -> attributeId.equals(v.getAttributeId()))
                .findFirst()
                .orElse(null);

        if (existingValue != null) {
            // Update existing field
            log.debug("Updating existing field: {} with value: {}", attributeId, value);

            var content = existingValue.getContent();
            if (content == null || content.isEmpty()) {
                content = new ArrayList<>();
                existingValue.setContent(content);
            }

            // Update first content or create new one
            if (content.isEmpty()) {
                var newContent = new Content<>();
                newContent.setValue(value);
                content.add(newContent);
            } else {
                content.get(0).setValue(value);
            }
        } else {
            // Add new field
            log.debug("Adding new field: {} with value: {}", attributeId, value);

            com.atp.product.controller.dto.response.atomm_responses.Value newValue = new com.atp.product.controller.dto.response.atomm_responses.Value();
            newValue.setAttributeId(attributeId);

            var content = new Content<>();
            content.setValue(value);

            List<Content> contentList = new ArrayList<>();
            contentList.add(content);
            newValue.setContent(contentList);

            values.add(newValue);
        }
    }*/

    /**
     * Updates the original order with the purchase order information from the response
     *
     * @param originalOrder The original order to update
     * @param response The purchase order response containing updated information
     */
    private void updateOriginalOrderWithPOInfo(AtommPurchaseOrder originalOrder,
                                               PurchaseOrderResponse response) {
        if (originalOrder == null || response == null || response.getPartsPurchaseOrder() == null) {
            log.warn("Cannot update order - original order or response is null");
            return;
        }

        try {
            // Get the purchase order details
            PurchaseOrderResponse.PartsPurchaseOrder po = response.getPartsPurchaseOrder();

            List<com.atp.product.controller.dto.response.atomm_responses.Value> values = originalOrder.getValues();

            // Update PO number and ID in the root Values
            updateOrAddValueField(values, "PONumber", po.getPoNumber());
            updateOrAddValueField(values, "PartsPurchaseOrderID", String.valueOf(po.getPartsPurchaseOrderID()));

            // Update the grand total (using POTotal)
            if (po.getPoTotal()) {
                updateOrAddPriceField(originalOrder.getValues(), "ECM_GrandTotal", po.getPoTotal());
            }

            // Update line items if available
            if (po.getLineItems() != null && !po.getLineItems().isEmpty() &&
                    originalOrder.getComplexValues() != null &&
                    originalOrder.getComplexValues().getPurchaseOrders() != null) {

                // Create a map of part numbers to their prices from the response
                Map<String, Double> partPrices = new HashMap<>();
                for (PartsPurchaseOrderLineItem item : po.getLineItems()) {
                    if (item.getPartNumber() != null && item.getCost() != null) {
                        partPrices.put(item.getPartNumber(), item.getCost());
                    }
                }

                // Update OrderCost for each purchase order item
                for (PurchaseOrder poItem : originalOrder.getComplexValues().getPurchaseOrders()) {
                    if (poItem.getValues() != null) {
                        String partNumber = extractPartNumber(poItem.getValues());
                        if (partNumber != null && !partNumber.equals(PART_NUMBER_NOT_AVAILABLE) &&
                                partPrices.containsKey(partNumber)) {
                            updateOrAddPriceField(poItem.getValues(), "OrderCost", partPrices.get(partNumber));
                        }
                    }
                }
            }

            log.info("Successfully updated order with PO information. PO: {}, ID: {}",
                    po.getPoNumber(), po.getPartsPurchaseOrderID());
        } catch (Exception e) {
            log.error("Error updating order with PO information: {}", e.getMessage(), e);
        }
    }

    /**
     * Helper method to update or add a value field in a Values object
     */
    private void updateOrAddValueField(List<com.atp.product.controller.dto.response.atomm_responses.Value> values, String fieldName, String fieldValue) {
        if (values == null) {
            return;
        }

        // Find existing value with the given attributeId
        for (Value value : values) {
            if (fieldName.equals(value.getAttributeId()) &&
                    value.getContent() != null &&
                    !value.getContent().isEmpty()) {
                // Update existing value
                value.getContent().get(0).setValue(fieldValue);
                return;
            }
        }

        // If we get here, the field doesn't exist yet - create it
        Content content = new Content();
        content.setValue(fieldValue);

        Value newValue = new Value();
        newValue.setAttributeId(fieldName);
        newValue.setContent(Collections.singletonList(content));

        values.add(newValue);
    }

    /**
     * Mark order as completed
     */
    private void markOrderAsCompleted(ScheduledOrder scheduledOrder) {
        Query query = new Query(Criteria.where(FIELD_ID).is(scheduledOrder.getId()));

        Update update = new Update()
                .set(FIELD_ACTIVE, false)
                .set(FIELD_STATUS, STATUS_COMPLETED)
                .set(FIELD_UPDATED_AT, LocalDateTime.now());

        mongoTemplate.updateFirst(query, update, ScheduledOrder.class);

        log.info("Marked scheduled order {} as completed", scheduledOrder.getId());
    }

    /**
     * Extract part number from Values object
     */
    private String extractPartNumber(Values values) {
        return Optional.ofNullable(values)
                .map(Values::getOrderPartNumber)
                .map(OrderPartNumber::getContent)
                .flatMap(list -> list.stream().findFirst())
                .map(Content::getValue)
                .map(Object::toString)
                .orElse("N/A");  // Default if missing
    }
    /**
     * Extract quantity from Values object
     */
    private String extractQuantity(Values values) {
        return Optional.ofNullable(values)
                .map(Values::getOrderQuantity)
                .map(OrderQuantity::getContent)
                .flatMap(list -> list.stream().findFirst())
                .map(Content::getValue)
                .map(Object::toString)
                .orElse(DEFAULT_QUANTITY_ZERO);  // Default if missing
    }

    /**
     * Extract cost from Values object
     */
    private String extractCost(Values values) {
        if (values.getOrderCost() != null &&
                values.getOrderCost().getContent() != null &&
                !values.getOrderCost().getContent().isEmpty()) {

            var content = values.getOrderCost().getContent().get(0);
            return content.getValue() != null ? content.getValue().toString() : "0.0";
        }
        return "0.0"; // Default cost
    }

    /**
     * Place order by calling third-party API
     *
     * @param originalOrder The original KarmakPurchaseOrder to be placed
     * @return true if order was placed successfully, false otherwise
     */
    public boolean placeOrder(AtommPurchaseOrder originalOrder) {
        // Input validation
        if (originalOrder == null) {
            throw new IllegalArgumentException("Original order cannot be null");
        }
        if (originalOrder.getExternalId() == null || originalOrder.getExternalId().trim().isEmpty()) {
            throw new IllegalArgumentException("Original order external ID cannot be null or empty");
        }

        log.info("Placing order for external ID: {}", originalOrder.getExternalId());

        try {
            // Convert originalOrder directly to JSON string
            String jsonPayload = objectMapper.writeValueAsString(originalOrder);
            log.debug("Order payload: {}", jsonPayload);

            // Create HTTP request
            MediaType mediaType = MediaType.parse(CONTENT_TYPE_APPLICATION_JSON);
            RequestBody body = RequestBody.create(jsonPayload, mediaType);

            Request.Builder requestBuilder = new Request.Builder()
                    .url(thirdPartyApiUrl)
                    .method("POST", body)
                    .addHeader(HEADER_CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON);

            // Get authentication token dynamically
            String authToken = getAuthToken();
            if (authToken != null && !authToken.trim().isEmpty()) {
                requestBuilder.addHeader(HEADER_AUTHORIZATION, authToken);
            } else {
                log.error("Failed to get authentication token for order: {}", originalOrder.getExternalId());
                return false;
            }

            Request request = requestBuilder.build();

            // Execute the request
            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    log.info("Order placed successfully for external ID: {}. Response code: {}",
                            originalOrder.getExternalId(), response.code());

                    // Log response body for debugging (optional)
                    if (response.body() != null) {
                        String responseBody = response.body().string();
                        log.debug("API Response: {}", responseBody);
                    }

                    return true;
                } else {
                    log.error("Failed to place order for external ID: {}. Response code: {}, Message: {}",
                            originalOrder.getExternalId(), response.code(), response.message());

                    // Log error response body if available
                    if (response.body() != null) {
                        String errorBody = response.body().string();
                        log.error("Error response body: {}", errorBody);
                    }

                    return false;
                }
            }

        } catch (IOException e) {
            log.error("IO Exception while placing order for external ID: {}",
                    originalOrder.getExternalId(), e);
            return false;
        } catch (Exception e) {
            log.error("Unexpected error while placing order for external ID: {}",
                    originalOrder.getExternalId(), e);
            return false;
        }
    }

    /**
     * Get authentication token from login API
     *
     * @return JWT token string or null if authentication fails
     */

    private String getAuthToken() {
        // Thread-safe check for cached token
        if (cachedToken != null && System.currentTimeMillis() < tokenExpiryTime) {
            log.debug("Using cached authentication token");
            return cachedToken;
        }

        // Synchronize token refresh to prevent multiple concurrent requests
        synchronized (tokenLock) {
            // Double-check pattern - token might have been refreshed by another thread
            if (cachedToken != null && System.currentTimeMillis() < tokenExpiryTime) {
                log.debug("Using cached authentication token (double-check)");
                return cachedToken;
            }

            log.info("Authenticating to get new token from: {}", loginApiUrl);

            try {
                // Create login request payload
                ObjectNode loginPayload = objectMapper.createObjectNode();
                loginPayload.put(JSON_USER_ID, apiUsername);
                loginPayload.put(JSON_PASSWORD, apiPassword);
                loginPayload.put(JSON_DEVICE_UUID, "");
                loginPayload.put(JSON_ENC, false);
                loginPayload.set(JSON_USER_PROPERTIES, objectMapper.createArrayNode());

                String loginJson = objectMapper.writeValueAsString(loginPayload);
                log.debug("Attempting authentication to: {}", loginApiUrl);

                // Create HTTP request for login
                MediaType mediaType = MediaType.parse(CONTENT_TYPE_APPLICATION_JSON);
                RequestBody body = RequestBody.create(loginJson, mediaType);

                Request request = new Request.Builder()
                        .url(loginApiUrl)
                        .method("POST", body)
                        .addHeader(HEADER_CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                        .build();

                // Execute login request
                try (Response response = httpClient.newCall(request).execute()) {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseBody = response.body().string();
                        log.debug("Login response: {}", responseBody);

                        // Parse response to extract token
                        JsonNode responseJson = objectMapper.readTree(responseBody);

                        if (responseJson.has(JSON_STATUS) && responseJson.get(JSON_STATUS).asInt() == 1) {
                            String token = responseJson.get(JSON_TOKEN).asText();

                            // Cache the token with configurable validity
                            cachedToken = token;
                            tokenExpiryTime = System.currentTimeMillis() + ((long) tokenCacheDurationMinutes * 60 * 1000);

                            log.info("Successfully authenticated and cached token");
                            return token;
                        } else {
                            log.error("Authentication failed. Status: {}", responseJson.get(JSON_STATUS));
                            return null;
                        }
                    } else {
                        log.error("Login request failed. Response code: {}, Message: {}",
                                response.code(), response.message());
                        return null;
                    }
                }

            } catch (Exception e) {
                log.error("Exception during authentication", e);
                return null;
            }
        } // End synchronized block
    }


}








