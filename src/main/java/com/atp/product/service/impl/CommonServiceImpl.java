package com.atp.product.service.impl;

import com.atp.product.controller.dto.request.InternalPurchaseOrderRequest;
import com.atp.product.controller.dto.request.PurchaseOrderRequest;
import com.atp.product.controller.dto.request.UpdateInventoryRequest;
import com.atp.product.controller.dto.response.ProductResponseHomePage;
import com.atp.product.controller.dto.response.PurchaseOrderResponse;
import com.atp.product.controller.dto.response.UpdateInventoryResponse;
import com.atp.product.exception.DomainException;
import com.atp.product.exception.bad_request.KarmakApiException;
import com.atp.product.utils.AuthorizationUtils;
import com.atp.product.utils.Constants;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import jakarta.servlet.http.HttpServletRequest;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
public class CommonServiceImpl {

    public static final Logger logger = LoggerFactory.getLogger(CommonServiceImpl.class);

    @Value("${aws.s3.bucket.name}")
    private String bucketName;

    @Value("${assets.root}")
    private String assetsRoot;

    @Value("${Karmak.partSearchURL}")
    private String karmaPartSearchURL;

    @Value("${Karmak.purchaseOrderURL}")
    private String karmakPoUrl;

    @Value("${Karmak.inventoryUpdateUrl}")
    private String karmakInventoryUpdateUrl;

    @Value("${Karmak.customerURL}")
    private String karmaCustomerURL;

    @Value("${Karmak.AccountNumber}")
    private String karmaAccountNumber;

    @Value("${Ocp-Apim-Subscription-Key}")
    private String subScriptionKey;

    private final S3ServiceImpl s3Service;
    private final OkHttpClient okHttpClient;
    private final ObjectMapper objectMapper;
    private final HttpServletRequest request;
    private final TaskExecutor executorAsyncThread;
    public CommonServiceImpl(S3ServiceImpl s3Service, OkHttpClient okHttpClient, ObjectMapper objectMapper, HttpServletRequest request, TaskExecutor executorAsyncThread) {
        this.s3Service = s3Service;
        this.okHttpClient = okHttpClient;
        this.objectMapper = objectMapper;
        this.request = request;
        this.executorAsyncThread = executorAsyncThread;
    }
    public String readFile(String filePath) throws IOException {
        return new String(Files.readAllBytes(Paths.get(filePath)));
    }

    //Existing Method Without optimize
   /* public List<ProductResponseHomePage> resizeImages(List<ProductResponseHomePage> results) {
        List<ProductResponseHomePage> modifiedResults = new ArrayList<>();
        try {
            for (ProductResponseHomePage product : results) {
                processProductImage(product);
                updateProductPriceAndQuantity(product);
                modifiedResults.add(product);
            }
        } catch (Exception e) {
            throw new DomainException("An error occurred while resizing images", e.getMessage());
        }
        return modifiedResults;
    }

    private void processProductImage(ProductResponseHomePage product) {
        String primaryImage = product.getPrimaryImage();
        if (isValidImage(primaryImage)) {
            String resizedImageUrl = getResizedImageUrl(product, primaryImage, "200x200");
            product.setPrimaryImage(resizedImageUrl);
        } else {
            product.setPrimaryImage("");
        }
    }

    private boolean isValidImage(String image) {
        return !image.isBlank();
    }

    private String getResizedImageUrl(ProductResponseHomePage product, String baseImageUrl, String specifiedSize) {
        String basePattern = baseImageUrl.substring(0, baseImageUrl.lastIndexOf('_')) + "_";
        if (product.getAssetMap() != null && !product.getAssetMap().isEmpty()) {
            for (Map.Entry<String, String> entry : product.getAssetMap().entrySet()) {
                String url = entry.getValue();
                if (url.startsWith(basePattern) && url.contains(specifiedSize)) {
                    return s3Service.generatePresignedUrl(bucketName, assetsRoot + "/" + url).toString();
                }
            }
        }
        return baseImageUrl; // Return original if resized image URL is not found
    }


    private void updateProductPriceAndQuantity(ProductResponseHomePage product) {
        if (!AuthorizationUtils.isAuthorizationHeaderValid(request)) {
            Map<String, String> priceAndQuantity = getPriceAndQuantityForProduct(product.getPartNumber());
            if (priceAndQuantity != null && !priceAndQuantity.isEmpty()) {

                // Use BigDecimal to parse and round the price
                BigDecimal price = new BigDecimal(priceAndQuantity.get(Constants.PRICE));
                price = price.setScale(2, RoundingMode.HALF_UP);

                product.setPrice(price.doubleValue());
                //product.setPrice(Double.parseDouble(priceAndQuantity.get(Constants.PRICE)));
                product.setQuantity(Integer.parseInt(priceAndQuantity.get(Constants.QUANTITY)));
                if (product.getProductQuantity() != 0 && product.getPrice() != 0.0) {
                    //product.setRowTotal(product.getProductQuantity() * product.getPrice());
                    BigDecimal productQuantity = new BigDecimal(product.getProductQuantity());
                    BigDecimal rowTotal = price.multiply(productQuantity).setScale(2, RoundingMode.HALF_UP);
                    product.setRowTotal(rowTotal.doubleValue());

                }
            }
        }
    }*/

   /* public Map<String,String> getPriceAndQuantityForProduct(String partNumber) {
        Map<String,String> finalResults = new HashMap<>();
        try {
            String karmakUrl = karmaPartSearchURL;
            String karmakAccount = karmaAccountNumber;
            String subscriptionKey = subScriptionKey;

            //getCustomerId from Header
            String karmakCustomerId = AuthorizationUtils.getKarmakCustomerId(request);



            // Initialize customerID
            String customerID = null;

            // Check if karmakCustomerId is null or empty
            if (karmakCustomerId != null && !karmakCustomerId.isEmpty()) {
                customerID = karmakCustomerId;  // If valid, assign the string value
            } else {
                customerID = "null";  // Otherwise, set customerID as null
            }

            log.info("int customer ID " + customerID);

            String requestJson = "{\"customerID\": " + customerID + "," + "\"locationID\": 1," + "\"region\": null,"
                    + "\"parts\": [{" + "\"number\": \"" + partNumber + "\"," + "\"description\": null,"
                    + "\"exactMatch\": true," + "\"source\": null," + "\"crossReference\": false," + "\"PageSize\": 1" + "}]" + "}";


            RequestBody body = RequestBody.create(requestJson, MediaType.parse("application/json"));
            Request partRequest = new Request.Builder().url(karmakUrl).addHeader("Content-Type", "application/json")
                    .addHeader("Cache-Control", "no-cache").addHeader("KarmakAccountNumber", karmakAccount)
                    .addHeader("Ocp-Apim-Subscription-Key", subscriptionKey).post(body).build();
            // Create Item in PLM
            Call call = okHttpClient.newCall(partRequest);
            Response partResponse = call.execute();
            int statusCode = partResponse.code();
            if (statusCode >= 200 && statusCode < 300) {
                JsonNode json = objectMapper.readValue(partResponse.body().string(), JsonNode.class);
                partResponse.close();
                if (Objects.nonNull(json) && json instanceof ArrayNode) {
                    ArrayNode results = (ArrayNode) json;
                    if (!results.isEmpty()) {
                        JsonNode entries = results.get(0).get("results");
                        if (entries != null) {
                            ArrayNode parts = (ArrayNode) entries;
                            JsonNode firstPart = parts.get(0);
                            if (firstPart != null) {
                                // Extract customerPrice and available fields
                                JsonNode priceNode = firstPart.get("price");
                                JsonNode availableNode = firstPart.get("available");

                                if (priceNode != null && availableNode != null) {
                                    finalResults.put(Constants.PRICE, priceNode.get("customerPrice").asText());
                                    finalResults.put(Constants.QUANTITY, availableNode.asText());
                                } else {
                                    logger.info("Price or available field is missing in the first element.");
                                }
                            } else {
                                logger.info("First element of 'results' array is missing.");
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new DomainException("An error occurred while calling karmak api", e.getMessage());
        }
        return finalResults;
    }*/

    public Map<String, String> getCustomerIdByCustomerKey(String karmakCustomerKey) {
        Map<String, String> result = new HashMap<>();
        try {
            String requestUrl = karmaCustomerURL + karmakCustomerKey;

            Request customerRequest = new Request.Builder()
                    .url(requestUrl)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Cache-Control", "no-cache")
                    .addHeader("KarmakAccountNumber", karmaAccountNumber)
                    .addHeader("Ocp-Apim-Subscription-Key", subScriptionKey)
                    .get()
                    .build();

            Call call = okHttpClient.newCall(customerRequest);
            Response customerResponse = call.execute();
            int statusCode = customerResponse.code();

            if (statusCode >= 200 && statusCode < 300) {
                JsonNode json = objectMapper.readTree(customerResponse.body().string());
                customerResponse.close();

                if (Objects.nonNull(json)) {
                    JsonNode results = json.get("Results");

                    if (results != null && results.isArray() && !results.isEmpty()) {
                        JsonNode firstResult = results.get(0);
                        JsonNode customerIdNode = firstResult.get("CustomerID");
                        JsonNode companyNameNode = firstResult.get("CompanyName");

                        if (customerIdNode != null && companyNameNode != null) {
                            result.put("CustomerID", customerIdNode.asText());
                            result.put("CompanyName", companyNameNode.asText());
                        } else {
                            logger.info("CustomerID or CompanyName field is missing in the response.");
                        }
                    } else {
                        logger.info("'Results' array is empty or missing in the response.");
                    }
                } else {
                    logger.info("Invalid JSON response from the Karmak API.");
                }
            } else {
                logger.error("Failed to fetch CustomerID. Status code: {}", statusCode);
            }
        } catch (Exception e) {
            throw new DomainException("An error occurred while calling the Karmak Customer API", e.getMessage());
        }
        return result;
    }

    public AggregationOperation documentToAggregationOperation(@NonNull Document document) {
        return context -> document;
    }

    //ForkJOin optimization for improve load performance

    private String getKarmakCustomerId() {
        return AuthorizationUtils.getKarmakCustomerId(request);  // Returns customer ID from request
    }

    public List<ProductResponseHomePage> resizeImages(List<ProductResponseHomePage> results) {
        List<CompletableFuture<Void>> futures = results.stream()
                .map(product -> CompletableFuture.runAsync(() -> {
                    try {
                        processProductImage(product);
                        updateProductPriceAndQuantity(product);  // Make sure this is handled asynchronously.
                    } catch (Exception e) {
                        logger.error("Error processing product: {}", product.getPartNumber(), e);
                    }
                }, executorAsyncThread))  // Use the executorAsyncThread (decorated executor)
                .collect(Collectors.toList());

        // Wait for all futures to complete
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        return results;
    }
    private void processProductImage(ProductResponseHomePage product) {
        String primaryImage = product.getPrimaryImage();
        if (isValidImage(primaryImage)) {
            String resizedImageUrl = getResizedImageUrl(product, primaryImage, "200x200");
            product.setPrimaryImage(resizedImageUrl);
        } else {
            product.setPrimaryImage("");
        }
    }

    private boolean isValidImage(String image) {
        return !image.isBlank();
    }

    private String getResizedImageUrl(ProductResponseHomePage product, String baseImageUrl, String specifiedSize) {
        String basePattern = baseImageUrl.substring(0, baseImageUrl.lastIndexOf('_')) + "_";
        if (product.getAssetMap() != null && !product.getAssetMap().isEmpty()) {
            for (Map.Entry<String, String> entry : product.getAssetMap().entrySet()) {
                String url = entry.getValue();
                if (url.startsWith(basePattern) && url.contains(specifiedSize)) {
                    return s3Service.generatePresignedUrl(bucketName, assetsRoot + "/" + url).toString();
                }
            }
        }
        return baseImageUrl;
    }

    private void updateProductPriceAndQuantity(ProductResponseHomePage product) {
        // Proceed only if the authorization header is valid
        if (AuthorizationUtils.isAuthorizationHeaderValid(request)) {
            CompletableFuture<Map<String, String>> priceAndQuantityFuture = getPriceAndQuantityForProductAsync(product.getPartNumber());

            priceAndQuantityFuture.thenAccept(priceAndQuantity -> {
                if (priceAndQuantity == null || priceAndQuantity.isEmpty()) {
                    logger.warn("Price and quantity data is not for this product: {}", product.getPartNumber());
                    return;
                }
                try {
                    if (priceAndQuantity != null && !priceAndQuantity.isEmpty()) {
                        // Use BigDecimal to parse and round the price
                        BigDecimal price = new BigDecimal(priceAndQuantity.get(Constants.PRICE));
                        price = price.setScale(2, RoundingMode.HALF_UP);

                        product.setPrice(price.doubleValue());
                        product.setQuantity(Integer.parseInt(priceAndQuantity.get(Constants.QUANTITY)));
                        product.setKarmakPartId(Integer.parseInt(priceAndQuantity.get(Constants.PARTS_INVENTORY_DETAIL_ID)));
                        product.setBranch(priceAndQuantity.get(Constants.BRANCH_CODE));

                        if (product.getProductQuantity() != 0 && product.getPrice() != 0.0) {
                            BigDecimal productQuantity = new BigDecimal(product.getProductQuantity());
                            BigDecimal rowTotal = price.multiply(productQuantity).setScale(2, RoundingMode.HALF_UP);
                            product.setRowTotal(rowTotal.doubleValue());
                        }
                    }
                } catch (NumberFormatException | ArithmeticException e) {
                    logger.error("Error processing price and quantity for product: {}", product.getPartNumber(), e);
                }
            }).exceptionally(ex -> {
                logger.error("Error fetching price and quantity for product: {}", product.getPartNumber(), ex);
                return null;
            }).join(); // Wait for completion if needed
        } else {
            logger.warn("Authorization failed for product: {}", product.getPartNumber());
        }
    }


    public CompletableFuture<Map<String, String>> getPriceAndQuantityForProductAsync(String partNumber) {
        Map<String, String> finalResults = new HashMap<>();
        try {

            String karmakUrl = karmaPartSearchURL;
            String karmakAccount = karmaAccountNumber;
            String subscriptionKey = subScriptionKey;

            String karmakCustomerId = getKarmakCustomerId();
            // Initialize customerID
            String customerID = null;

            // Check if karmakCustomerId is null or empty
            if (karmakCustomerId != null && !karmakCustomerId.isEmpty()) {
                customerID = karmakCustomerId;  // If valid, assign the string value
            } else {
                customerID = "null";  // Otherwise, set customerID as null
            }

            log.info("int customer ID " + customerID);

            String requestJson = "{\"customerID\": " + customerID + "," + "\"locationID\": 1," + "\"region\": null,"
                    + "\"parts\": [{" + "\"number\": \"" + partNumber + "\"," + "\"description\": null,"
                    + "\"exactMatch\": true," + "\"source\": null," + "\"crossReference\": false," + "\"PageSize\": 1" + "}]" + "}";


            RequestBody body = RequestBody.create(requestJson, MediaType.parse("application/json"));
            Request partRequest = new Request.Builder().url(karmakUrl).addHeader("Content-Type", "application/json")
                    .addHeader("Cache-Control", "no-cache").addHeader("KarmakAccountNumber", karmakAccount)
                    .addHeader("Ocp-Apim-Subscription-Key", subscriptionKey).post(body).build();
            // Create Item in PLM
            Call call = okHttpClient.newCall(partRequest);
            Response partResponse = call.execute();
            int statusCode = partResponse.code();
            if (statusCode >= 200 && statusCode < 300) {
                JsonNode json = objectMapper.readValue(partResponse.body().string(), JsonNode.class);
                partResponse.close();
                if (Objects.nonNull(json) && json instanceof ArrayNode) {
                    ArrayNode results = (ArrayNode) json;
                    if (!results.isEmpty()) {
                        JsonNode entries = results.get(0).get("results");
                        if (entries != null) {
                            ArrayNode parts = (ArrayNode) entries;
                            JsonNode firstPart = parts.get(0);
                            if (firstPart != null) {
                                // Extract customerPrice and available fields
                                JsonNode priceNode = firstPart.get("price");
                                JsonNode availableNode = firstPart.get("available");
                                JsonNode inventoryDetailIdNode = firstPart.get("PartsInventoryDetailID");
                                JsonNode branchCodeNode = firstPart.get("branchCode");

                                if (priceNode != null && availableNode != null) {
                                    finalResults.put(Constants.PRICE, priceNode.get("customerPrice").asText());
                                    finalResults.put(Constants.QUANTITY, availableNode.asText());
                                    finalResults.put(Constants.PARTS_INVENTORY_DETAIL_ID, Integer.toString(inventoryDetailIdNode.asInt()));
                                    finalResults.put(Constants.BRANCH_CODE, branchCodeNode.asText());
                                } else {
                                    logger.info("Price or available field is missing in the first element.");
                                }
                            } else {
                                logger.info("First element of 'results' array is missing.");
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new DomainException("An error occurred while calling karmak api", e.getMessage());
        }
        return CompletableFuture.completedFuture(finalResults);
    }

    //new method with internal DTO
    public PurchaseOrderResponse createPurchaseOrder(InternalPurchaseOrderRequest internalRequest) {
        try {
            if (internalRequest.getPoDate() == null) {
                internalRequest.setPoDate(LocalDate.now());
            }

            // Convert to the external API format (PurchaseOrderRequest)
            PurchaseOrderRequest karmakRequest = new PurchaseOrderRequest();
            karmakRequest.setLocationID(internalRequest.getLocationID());
            karmakRequest.setFillingLocationID(internalRequest.getFillingLocationID());
            karmakRequest.setApVendorID(internalRequest.getApVendorID());
            karmakRequest.setOrderedBy(internalRequest.getOrderedBy());
            karmakRequest.setSupplierID(internalRequest.getSupplierID());
            karmakRequest.setPoDate(internalRequest.getPoDate());
            karmakRequest.setDirectShip(internalRequest.isDirectShip());
            karmakRequest.setShipping(internalRequest.getShipping());

            List<PurchaseOrderRequest.LineItem> karmakLineItems = internalRequest.getLineItems().stream()
                    .map(line -> {
                        PurchaseOrderRequest.LineItem item = new PurchaseOrderRequest.LineItem();
                        item.setPartID(line.getPartID());
                        item.setQuantity(line.getQuantity());
                        item.setCost(line.getCost());
                        item.setMessage(line.getMessage());
                        return item;
                    }).toList();

            karmakRequest.setLineItems(karmakLineItems);

            // Send to Karmak
            String json = objectMapper.writeValueAsString(karmakRequest);
            RequestBody body = RequestBody.create(json, MediaType.parse("application/json"));

            Request httpRequest = new Request.Builder()
                    .url(karmakPoUrl)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Cache-Control", "no-cache")
                    .addHeader("KarmakAccountNumber", karmaAccountNumber)
                    .addHeader("Ocp-Apim-Subscription-Key", subScriptionKey)
                    .post(body)
                    .build();

            try (Response response = okHttpClient.newCall(httpRequest).execute()) {
                if (response.code() == 201) {
                    assert response.body() != null;
                    PurchaseOrderResponse poResponse = objectMapper.readValue(response.body().string(), PurchaseOrderResponse.class);

                    // Combine internal request & external response to update inventory
                    List<InternalPurchaseOrderRequest.InternalLineItem> originalItems = internalRequest.getLineItems();
                    List<PurchaseOrderResponse.LineItem> responseItems = poResponse.getPartsPurchaseOrder().getLineItems();

                    List<UpdateInventoryRequest> inventoryUpdates = IntStream.range(0, responseItems.size())
                            .mapToObj(i -> {
                                PurchaseOrderResponse.LineItem responseItem = responseItems.get(i);
                                InternalPurchaseOrderRequest.InternalLineItem originalItem = originalItems.get(i);

                                UpdateInventoryRequest update = new UpdateInventoryRequest();
                                update.setBranch(originalItem.getBranch()); // comes from request
                                update.setPartNumber(responseItem.getPartNumber()); // from response
                                update.setSupplier(responseItem.getSupplier());     // from response
                                update.setQuantityAvailable((double) originalItem.getTotalQuantity() - originalItem.getQuantity());
                                return update;
                            })
                            .toList();

                    updateInventory(inventoryUpdates);

                    return poResponse;
                } else {
                    String error = response.body() != null ? response.body().string() : "No error body";
                    logger.error("PO creation failed. Code: {}, Body: {}", response.code(), error);
                    throw new KarmakApiException("Failed to create Purchase Order. " + error);
                }
            }

        } catch (Exception e) {
            logger.error("Exception during PO creation", e);
            throw new KarmakApiException("Exception occurred while creating PO: " + e.getMessage(), e);
        }
    }

    public List<UpdateInventoryResponse> updateInventory(List<UpdateInventoryRequest> inventoryList) {
        try {
            String json = objectMapper.writeValueAsString(inventoryList);
            RequestBody body = RequestBody.create(json, MediaType.parse("application/json"));

            Request jsonRequest = new Request.Builder()
                    .url(karmakInventoryUpdateUrl)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Cache-Control", "no-cache")
                    .addHeader("KarmakAccountNumber", karmaAccountNumber)
                    .addHeader("Ocp-Apim-Subscription-Key", subScriptionKey)
                    .put(body)
                    .build();

            try (Response response = okHttpClient.newCall(jsonRequest).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    return objectMapper.readValue(response.body().string(), new TypeReference<List<UpdateInventoryResponse>>() {
                    });
                } else {
                    String error = response.body() != null ? response.body().string() : "No error body";
                    logger.error("Inventory update failed. Code: {}, Body: {}", response.code(), error);
                    throw new KarmakApiException("Failed to update inventory. " + error);
                }
            }
        } catch (Exception e) {
            logger.error("Exception during inventory update", e);
            throw new KarmakApiException("Inventory update error: " + e.getMessage(), e);
        }
    }
}
