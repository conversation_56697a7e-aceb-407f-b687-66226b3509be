package com.atp.product.service.impl;

import com.atp.product.controller.dto.response.atomm_responses.*;
import com.atp.product.service.InvoiceService;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Service implementation for invoice generation
 */
@Service
@Slf4j
public class InvoiceServiceImpl implements InvoiceService {

    private final PurchaseOrderService purchaseOrderService;
    private final MongoTemplate mongoTemplate;

    @Autowired
    public InvoiceServiceImpl(PurchaseOrderService purchaseOrderService, MongoTemplate mongoTemplate) {
        this.purchaseOrderService = purchaseOrderService;
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public ByteArrayResource generateInvoice(String name, String email, String externalId) {
        log.info("Generating invoice for customer: {}, email: {}, externalId: {}", name, email, externalId);

        try {
            // DEBUG: First check what's in the database
            debugDatabaseContent(externalId);

            // OPTIMIZED: Use direct MongoDB aggregation for faster data retrieval
            AtommPurchaseOrder order = getOrderByExternalIdOptimized(externalId);

            if (order == null) {
                log.warn("Purchase order not found for externalId: {}", externalId);
                return null;
            }

            return generatePdfInvoice(order, name, email);

        } catch (Exception e) {
            log.error("Error generating invoice for externalId: {}", externalId, e);
            throw new RuntimeException("Failed to generate invoice", e);
        }
    }

    @Override
    public ByteArrayResource generateInvoiceById(String orderId, String name, String email) {
        log.info("Generating invoice for orderId: {}, customer: {}, email: {}", orderId, name, email);

        try {
            // OPTIMIZED: Use direct MongoDB aggregation for faster data retrieval
            AtommPurchaseOrder order = getOrderByIdOptimized(orderId);

            if (order == null) {
                log.warn("Purchase order not found for orderId: {}", orderId);
                return null;
            }

            return generatePdfInvoice(order, name, email);

        } catch (Exception e) {
            log.error("Error generating invoice for orderId: {}", orderId, e);
            throw new RuntimeException("Failed to generate invoice", e);
        }
    }

    /**
     * Generate PDF invoice from purchase order data
     */
    private ByteArrayResource generatePdfInvoice(AtommPurchaseOrder order, String customerName, String customerEmail) {
        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            PdfWriter writer = new PdfWriter(out);
            PdfDocument pdfDocument = new PdfDocument(writer);
            Document document = new Document(pdfDocument, PageSize.A4);
            document.setMargins(30, 30, 30, 30);

            // Extract order information from Values
            String orderDate = extractOrderDate(order.getValues());
            String deliveryMethod = extractDeliveryMethod(order.getValues());
            String binLocation = extractBinLocation(order.getValues());

            // Extract PartsPurchaseOrderID from Values if available
            String partsPurchaseOrderId = extractPartsPurchaseOrderId(order.getValues());

            // Get current date for invoice if order date not available
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd MMM yyyy, HH:mm a");
            String invoiceDate = orderDate != null ? orderDate : dateFormat.format(new Date());

            // Add company logo and title
            addCompanyLogoAndTitle(document);
            document.add(new Paragraph(" "));

            // Invoice Info - Enhanced with order details
            Table invoiceDetails = new Table(UnitValue.createPercentArray(new float[]{50, 50}))
                    .useAllAvailableWidth();

            String invoiceNumber = order.getExternalId() != null ? order.getExternalId() :
                                 (order.getExternalId() != null ? order.getExternalId() : "N/A");

            invoiceDetails.addCell(new Cell().add(new Paragraph("Invoice No: #" + invoiceNumber))
                    .setBorder(Border.NO_BORDER).setBold());
            invoiceDetails.addCell(new Cell().add(new Paragraph("Order Date: " + invoiceDate))
                    .setTextAlignment(TextAlignment.RIGHT).setBold()
                    .setBorder(Border.NO_BORDER));

            // Add PartsPurchaseOrderId if available
            if (partsPurchaseOrderId != null) {
                invoiceDetails.addCell(new Cell().add(new Paragraph("Parts Purchase Order ID: " + partsPurchaseOrderId))
                        .setBorder(Border.NO_BORDER).setBold());
            } else {
                invoiceDetails.addCell(new Cell().add(new Paragraph(""))
                        .setBorder(Border.NO_BORDER));
            }

            // Add delivery method
            if (deliveryMethod != null) {
                invoiceDetails.addCell(new Cell().add(new Paragraph("Delivery Method: " + deliveryMethod))
                        .setTextAlignment(TextAlignment.RIGHT)
                        .setBorder(Border.NO_BORDER));
            } else {
                invoiceDetails.addCell(new Cell().add(new Paragraph(""))
                        .setTextAlignment(TextAlignment.RIGHT)
                        .setBorder(Border.NO_BORDER));
            }

            document.add(invoiceDetails);
            document.add(new Paragraph(" "));

            // Customer and Company Info - Enhanced with delivery information
            Table infoTable = new Table(UnitValue.createPercentArray(new float[]{50, 50}))
                    .useAllAvailableWidth();

            // Build customer info - simplified
            StringBuilder customerInfoBuilder = new StringBuilder();
            customerInfoBuilder.append(customerName).append("\n");
            customerInfoBuilder.append("Email: ").append(customerEmail);

            infoTable.addCell(new Cell().add(new Paragraph()
                            .add(new Text("Customer Information:\n").setBold().setUnderline())
                            .add(customerInfoBuilder.toString()))
                    .setBorder(Border.NO_BORDER));

            // Company information based on delivery method and bin location
            String sectionTitle = "Sold By:";
            if ("Ship to Address".equalsIgnoreCase(deliveryMethod)) {
                sectionTitle = "Bill To:";
            }

            StringBuilder companyInfo = new StringBuilder();

            // Determine store location based on bin location
            if ("Store Pick Up".equalsIgnoreCase(deliveryMethod)) {
                if ("Rockdale".equalsIgnoreCase(binLocation) || "RCK".equalsIgnoreCase(binLocation)) {
                    // Rockdale store;
                    companyInfo.append("Action Truck Parts\n");
                    companyInfo.append("1801 Moen Ave,Rockdale, IL 60436\n");
                    companyInfo.append("Call or Text Us!\n");
                    companyInfo.append("815-744-7800\n");
                    companyInfo.append("Hours:\n");
                    companyInfo.append("M–F: 8am – 5pm\n");
                    companyInfo.append("Sat: 8am – 12pm");
                } else {
                    // Bolingbrook store (default)
                    companyInfo.append("Action Truck Parts \n");
                    companyInfo.append("1 Seidel Ct,Bolingbrook, IL 60490\n");
                    companyInfo.append("Call or Text Us!\n");
                    companyInfo.append("************\n");
                    companyInfo.append("Hours:\n");
                    companyInfo.append("M–F: 8am – 5pm\n");
                    companyInfo.append("Sat: 8am – 12pm");
                }
            } else {
                // Ship to Address - show bin location info
                companyInfo.append("ATP Parts Company\n");
                companyInfo.append("Email: <EMAIL>\n");
                companyInfo.append("Phone: (*************");
                if (binLocation != null) {
                    companyInfo.append("\nBill To: ").append(binLocation);
                }
            }

            infoTable.addCell(new Cell().add(new Paragraph()
                            .add(new Text(sectionTitle + "\n").setBold().setUnderline())
                            .add(companyInfo.toString()))
                    .setTextAlignment(TextAlignment.RIGHT)
                    .setBorder(Border.NO_BORDER));

            document.add(infoTable);
            document.add(new Paragraph(" "));

            // Items Table - Enhanced with supplier information
            Table table = new Table(UnitValue.createPercentArray(new float[]{50, 15, 15, 20}))
                    .useAllAvailableWidth();

            table.addHeaderCell(new Cell().add(new Paragraph("Product").setBold()));
            //table.addHeaderCell(new Cell().add(new Paragraph("Supplier").setBold()));
            table.addHeaderCell(new Cell().add(new Paragraph("Quantity").setBold()));
            table.addHeaderCell(new Cell().add(new Paragraph("Unit Price").setBold()));
            table.addHeaderCell(new Cell().add(new Paragraph("Total").setBold()));

            double grandTotal = 0;

            // Extract items from ComplexValues
            if (order.getComplexValues() != null && 
                order.getComplexValues().getPurchaseOrders() != null) {
                
                List<PurchaseOrder> purchaseOrders = order.getComplexValues().getPurchaseOrders();
                
                for (PurchaseOrder po : purchaseOrders) {
                    if (po.getValues() != null) {
                        Values values = po.getValues();
                        
                        // Extract item details including supplier
                        String itemName = extractStringValue(values.getOrderProductDetails(), "Product Details");
                        String partNumber = extractStringValue(values.getOrderPartNumber(), "Part Number");
                        //String supplierName = extractStringValue(values.getOrderSupplierName(), "Supplier");
                        double quantity = extractDoubleValue(values.getOrderQuantity(), 1.0);
                        double cost = extractDoubleValue(values.getOrderCost(), 0.0);
                        double itemTotal = quantity * cost;

                        grandTotal += itemTotal;

                        // Add item to table with supplier information
                        String displayName = itemName + "\n(Part #: " + partNumber + ")";
                        table.addCell(new Cell().add(new Paragraph(displayName)));
                        /*table.addCell(new Cell().add(new Paragraph(supplierName != null ? supplierName : "N/A"))
                                .setTextAlignment(TextAlignment.CENTER));*/
                        table.addCell(new Cell().add(new Paragraph(String.valueOf((int)quantity)))
                                .setTextAlignment(TextAlignment.RIGHT));
                        table.addCell(new Cell().add(new Paragraph(String.format("$ %.2f", cost)))
                                .setTextAlignment(TextAlignment.RIGHT));
                        table.addCell(new Cell().add(new Paragraph(String.format("$ %.2f", itemTotal)))
                                .setTextAlignment(TextAlignment.RIGHT));
                    }
                }
            }

            // If no items found in ComplexValues, try to get total from Values
            if (grandTotal == 0) {
                grandTotal = extractGrandTotalFromValues(order.getValues());

                // Add a generic item row with 5 columns
                table.addCell(new Cell().add(new Paragraph("Purchase Order Items")));
                table.addCell(new Cell().add(new Paragraph("Various"))
                        .setTextAlignment(TextAlignment.CENTER));
                table.addCell(new Cell().add(new Paragraph("1"))
                        .setTextAlignment(TextAlignment.RIGHT));
                table.addCell(new Cell().add(new Paragraph(String.format("$ %.2f", grandTotal)))
                        .setTextAlignment(TextAlignment.RIGHT));
                table.addCell(new Cell().add(new Paragraph(String.format("$ %.2f", grandTotal)))
                        .setTextAlignment(TextAlignment.RIGHT));
            }

            document.add(table);
            document.add(new Paragraph(" "));

            // Total Amount - Updated for 5 column layout
            Table totalTable = new Table(UnitValue.createPercentArray(new float[]{35, 15, 15, 15, 20}))
                    .useAllAvailableWidth();

            totalTable.addCell(new Cell(0, 4).add(new Paragraph("Grand Total").setBold()));
            totalTable.addCell(new Cell().add(new Paragraph(String.format("$ %.2f", grandTotal)).setBold())
                    .setTextAlignment(TextAlignment.RIGHT));

            document.add(totalTable);
            document.add(new Paragraph(" "));

            // Footer - Enhanced with delivery-specific information
            document.add(new Paragraph("Payment Method: Purchase Order")
                    .setBold()
                    .setFontSize(10)
                    .setUnderline());

            // Add delivery-specific footer information
            StringBuilder footerInfo = new StringBuilder();
            footerInfo.append("\nThank you for your business!\n");

            if (deliveryMethod != null) {
                if ("Store Pick Up".equalsIgnoreCase(deliveryMethod)) {
                    /*footerInfo.append("Please bring this invoice when picking up your order.\n");
                    footerInfo.append("Items will be held for 30 days from order date.\n");*/
                } else if ("Ship to Address".equalsIgnoreCase(deliveryMethod)) {
                    footerInfo.append("Your order will be shipped to the specified address.\n");
                    footerInfo.append("Please ensure someone is available to receive the delivery.\n");
                }
            }

            footerInfo.append("This is a system-generated invoice and does not require a signature.");

            document.add(new Paragraph(footerInfo.toString())
                    .setFontSize(9)
                    .setTextAlignment(TextAlignment.CENTER));

            document.close();

            return new ByteArrayResource(out.toByteArray());

        } catch (Exception e) {
            log.error("Error generating PDF invoice", e);
            throw new RuntimeException("Failed to generate PDF invoice", e);
        }
    }

    /**
     * Extract string value from order attribute
     */
    private String extractStringValue(Object attribute, String defaultValue) {
        if (attribute == null) return defaultValue;
        
        try {
            // Use reflection to get content
            java.lang.reflect.Method getContentMethod = attribute.getClass().getMethod("getContent");
            @SuppressWarnings("unchecked")
            List<Content> contentList = (List<Content>) getContentMethod.invoke(attribute);
            
            if (contentList != null && !contentList.isEmpty()) {
                Object value = contentList.get(0).getValue();
                return value != null ? value.toString() : defaultValue;
            }
        } catch (Exception e) {
            log.debug("Failed to extract string value from attribute", e);
        }
        
        return defaultValue;
    }

    /**
     * Extract double value from order attribute
     */
    private double extractDoubleValue(Object attribute, double defaultValue) {
        if (attribute == null) return defaultValue;
        
        try {
            // Use reflection to get content
            java.lang.reflect.Method getContentMethod = attribute.getClass().getMethod("getContent");
            @SuppressWarnings("unchecked")
            List<Content> contentList = (List<Content>) getContentMethod.invoke(attribute);
            
            if (contentList != null && !contentList.isEmpty()) {
                Object value = contentList.get(0).getValue();
                if (value instanceof Number) {
                    return ((Number) value).doubleValue();
                } else if (value instanceof String) {
                    return Double.parseDouble((String) value);
                }
            }
        } catch (Exception e) {
            log.debug("Failed to extract double value from attribute", e);
        }
        
        return defaultValue;
    }

    /**
     * Extract grand total from Values array
     */
    private double extractGrandTotalFromValues(List<com.atp.product.controller.dto.response.atomm_responses.Value> values) {
        if (values == null) return 0.0;
        
        for (com.atp.product.controller.dto.response.atomm_responses.Value value : values) {
            if ("ECM_GrandTotal".equals(value.getAttributeId())) {
                List<Content> content = value.getContent();
                if (content != null && !content.isEmpty()) {
                    Object val = content.get(0).getValue();
                    if (val instanceof Number) {
                        return ((Number) val).doubleValue();
                    } else if (val instanceof String) {
                        try {
                            return Double.parseDouble((String) val);
                        } catch (NumberFormatException e) {
                            log.debug("Failed to parse grand total: {}", val);
                        }
                    }
                }
            }
        }
        
        return 0.0;
    }

    // ==================== OPTIMIZED DATABASE ACCESS METHODS ====================

    /**
     * OPTIMIZED: Get order by external ID using direct MongoDB query
     * This is faster than service layer as it only fetches required fields
     * FIXED: Uses correct collection name "entity" and proper field mapping
     */
    private AtommPurchaseOrder getOrderByExternalIdOptimized(String externalId) {
        log.debug("Fetching order with optimized query for externalId: {}", externalId);

        try {
            // Direct MongoDB query with field projection - using correct collection name "entity"
            Query query = new Query(Criteria.where("externalId").is(externalId));

            // Include only fields needed for invoice generation
            query.fields()
                .include("_id")
                .include("externalId")
                .include("values")
                .include("complexValues")
                .include("parentId")
                .include("entityType")
                .include("workspaceId")
                .include("datatypeDefinitionId");

            // Use the correct collection name "entity" as used in PurchaseOrderService
            org.bson.Document doc = mongoTemplate.findOne(query, org.bson.Document.class, "entity");

            if (doc == null) {
                log.debug("No order found for externalId: {}", externalId);
                return null;
            }

            // Convert Document to AtommPurchaseOrder using the same approach as PurchaseOrderService
            AtommPurchaseOrder order = convertDocumentToKarmakPurchaseOrder(doc);

            log.debug("Successfully fetched order using optimized query for externalId: {}", externalId);
            return order;

        } catch (Exception e) {
            log.warn("Optimized query failed for externalId: {}, falling back to service layer", externalId, e);
            // Fallback to service layer if direct query fails
            return purchaseOrderService.getPurchaseOrderByExternalId(externalId);
        }
    }

    /**
     * OPTIMIZED: Get order by ID using direct MongoDB query with field projection
     * This is faster than service layer as it only fetches required fields
     */
    private AtommPurchaseOrder getOrderByIdOptimized(String orderId) {
        log.debug("Fetching order with optimized query for orderId: {}", orderId);

        try {
            // Direct MongoDB query with field projection
            Query query = new Query(Criteria.where("_id").is(orderId));

            // Include only fields needed for invoice generation
            query.fields()
                .include("_id")
                .include("externalId")
                .include("values")
                .include("complexValues.purchaseOrders")
                .include("createdAt")
                .include("updatedAt");

            // Use the correct collection name "entity" as used in PurchaseOrderService
            org.bson.Document doc = mongoTemplate.findOne(query, org.bson.Document.class, "entity");

            if (doc == null) {
                log.debug("No order found for orderId: {}", orderId);
                return null;
            }

            // Convert Document to AtommPurchaseOrder using the same approach as PurchaseOrderService
            AtommPurchaseOrder order = convertDocumentToKarmakPurchaseOrder(doc);

            log.debug("Successfully fetched order using optimized query for orderId: {}", orderId);
            return order;

        } catch (Exception e) {
            log.warn("Optimized query failed for orderId: {}, falling back to service layer", orderId, e);
            // Fallback to service layer if direct query fails
            return purchaseOrderService.getPurchaseOrderByExternalId(orderId);
        }
    }

    /**
     * ALTERNATIVE: Ultra-fast aggregation that only gets essential invoice data
     * Use this if you want maximum performance and don't mind custom data structure
     */
    private InvoiceData getInvoiceDataOptimized(String externalId) {
        log.debug("Fetching minimal invoice data for externalId: {}", externalId);

        try {
            Aggregation aggregation = Aggregation.newAggregation(
                // Match the document
                Aggregation.match(Criteria.where("externalId").is(externalId)),
                // Project only essential invoice fields
                Aggregation.project()
                    .and("externalId").as("invoiceNumber")
                    .and("values").as("values")
                    .and("complexValues.purchaseOrders.values.orderPartNumber").as("partNumbers")
                    .and("complexValues.purchaseOrders.values.orderProductDetails").as("productDetails")
                    .and("complexValues.purchaseOrders.values.orderQuantity").as("quantities")
                    .and("complexValues.purchaseOrders.values.orderCost").as("costs")
            );

            // This would return a custom InvoiceData object with only needed fields
            // Fastest option but requires custom data structure

            return null; // Placeholder - implement if needed

        } catch (Exception e) {
            log.error("Failed to fetch optimized invoice data", e);
            return null;
        }
    }

    /**
     * Convert MongoDB Document to AtommPurchaseOrder
     * Simplified version for invoice generation - only maps essential fields
     */
    private AtommPurchaseOrder convertDocumentToKarmakPurchaseOrder(org.bson.Document doc) {
        try {
            AtommPurchaseOrder order = new AtommPurchaseOrder();

            // Basic fields
            //order.setId(doc.getObjectId("_id") != null ? doc.getObjectId("_id").toString() : null);
            order.setExternalId(doc.getString("externalId"));
            order.setParentId(doc.getString("parentId"));
            order.setEntityType(doc.getString("entityType"));
            order.setWorkspaceId(doc.getString("workspaceId"));

            // Convert Values object (not array) - based on your DB structure
            org.bson.Document valuesDoc = (org.bson.Document) doc.get("values");
            if (valuesDoc != null) {
                List<com.atp.product.controller.dto.response.atomm_responses.Value> values = new ArrayList<>();

                // Extract each field from the values document
                for (String fieldName : valuesDoc.keySet()) {
                    org.bson.Document fieldDoc = (org.bson.Document) valuesDoc.get(fieldName);
                    if (fieldDoc != null) {
                        com.atp.product.controller.dto.response.atomm_responses.Value value = new com.atp.product.controller.dto.response.atomm_responses.Value();
                        value.setAttributeId(fieldName);

                        // Handle the nested English structure
                        org.bson.Document englishDoc = (org.bson.Document) fieldDoc.get("English");
                        if (englishDoc != null) {
                            @SuppressWarnings("unchecked")
                            List<org.bson.Document> contentDocs = (List<org.bson.Document>) englishDoc.get("content");
                            if (contentDocs != null) {
                                List<Content> contentList = new ArrayList<>();
                                for (org.bson.Document contentDoc : contentDocs) {
                                    Content content = new Content();
                                    // Handle different value types (text, date, number)
                                    Object valueObj = contentDoc.get("text");
                                    if (valueObj == null) {
                                        valueObj = contentDoc.get("value");
                                    }
                                    content.setValue(valueObj);
                                    contentList.add(content);
                                }
                                value.setContent(contentList);
                            }
                        }
                        values.add(value);
                    }
                }
                order.setValues(values);
            }

            // Convert ComplexValues - based on your DB structure with "PurchaseOrders"
            org.bson.Document complexValuesDoc = (org.bson.Document) doc.get("complexValues");
            if (complexValuesDoc != null) {
                ComplexValues complexValues = new ComplexValues();

                // Handle "PurchaseOrders" (capital P) from your database structure
                @SuppressWarnings("unchecked")
                List<org.bson.Document> purchaseOrdersDocs = (List<org.bson.Document>) complexValuesDoc.get("PurchaseOrders");
                if (purchaseOrdersDocs != null) {
                    List<PurchaseOrder> purchaseOrders = new ArrayList<>();
                    for (org.bson.Document poDoc : purchaseOrdersDocs) {
                        PurchaseOrder po = new PurchaseOrder();
                        po.setType(poDoc.getString("type"));
                        po.setName(poDoc.getString("name"));

                        // Convert Values for this purchase order
                        org.bson.Document valuesDocPO = (org.bson.Document) poDoc.get("values");
                        if (valuesDocPO != null) {
                            Values values = convertDocumentToValuesFromComplexStructure(valuesDocPO);
                            po.setValues(values);
                        }

                        purchaseOrders.add(po);
                    }
                    complexValues.setPurchaseOrders(purchaseOrders);
                }
                order.setComplexValues(complexValues);
            }

            return order;

        } catch (Exception e) {
            log.error("Error converting Document to AtommPurchaseOrder", e);
            throw new RuntimeException("Failed to convert document", e);
        }
    }

    /**
     * Convert Document to Values object for purchase order items from complex structure
     * This handles the nested English structure in your database
     */
    private Values convertDocumentToValuesFromComplexStructure(org.bson.Document valuesDoc) {
        Values values = new Values();

        // Extract common fields needed for invoice generation from the complex structure
        values.setOrderPartNumber(extractOrderAttributeFromComplexStructure(valuesDoc, "OrderPartNumber", OrderPartNumber.class));
        values.setOrderProductDetails(extractOrderAttributeFromComplexStructure(valuesDoc, "OrderProductDetails", OrderProductDetails.class));
        values.setOrderQuantity(extractOrderAttributeFromComplexStructure(valuesDoc, "OrderQuantity", OrderQuantity.class));
        values.setOrderCost(extractOrderAttributeFromComplexStructure(valuesDoc, "OrderCost", OrderCost.class));
        values.setOrderSupplierName(extractOrderAttributeFromComplexStructure(valuesDoc, "OrderSupplierName", OrderSupplierName.class));

        return values;
    }

    /**
     * Convert Document to Values object for purchase order items (legacy method)
     */
    private Values convertDocumentToValues(org.bson.Document valuesDoc) {
        Values values = new Values();

        // Extract common fields needed for invoice generation
        values.setOrderPartNumber(extractOrderAttribute(valuesDoc, "orderPartNumber", OrderPartNumber.class));
        values.setOrderProductDetails(extractOrderAttribute(valuesDoc, "orderProductDetails", OrderProductDetails.class));
        values.setOrderQuantity(extractOrderAttribute(valuesDoc, "orderQuantity", OrderQuantity.class));
        values.setOrderCost(extractOrderAttribute(valuesDoc, "orderCost", OrderCost.class));

        return values;
    }

    /**
     * Extract order attribute from complex structure (handles English nested structure)
     */
    @SuppressWarnings("unchecked")
    private <T> T extractOrderAttributeFromComplexStructure(org.bson.Document valuesDoc, String fieldName, Class<T> clazz) {
        try {
            org.bson.Document fieldDoc = (org.bson.Document) valuesDoc.get(fieldName);
            if (fieldDoc == null) return null;

            T instance = clazz.getDeclaredConstructor().newInstance();

            // Set attributeId
            java.lang.reflect.Method setAttributeIdMethod = clazz.getMethod("setAttributeId", String.class);
            setAttributeIdMethod.invoke(instance, fieldName);

            // Handle the nested English structure
            org.bson.Document englishDoc = (org.bson.Document) fieldDoc.get("English");
            if (englishDoc != null) {
                List<org.bson.Document> contentDocs = (List<org.bson.Document>) englishDoc.get("content");
                if (contentDocs != null) {
                    List<Content> contentList = new ArrayList<>();
                    for (org.bson.Document contentDoc : contentDocs) {
                        Content content = new Content();
                        // Get the text value from the content
                        Object textValue = contentDoc.get("text");
                        content.setValue(textValue);
                        contentList.add(content);
                    }

                    java.lang.reflect.Method setContentMethod = clazz.getMethod("setContent", List.class);
                    setContentMethod.invoke(instance, contentList);
                }
            }

            return instance;

        } catch (Exception e) {
            log.debug("Failed to extract {} from complex structure", fieldName, e);
            return null;
        }
    }

    /**
     * Extract order attribute from document (legacy method)
     */
    @SuppressWarnings("unchecked")
    private <T> T extractOrderAttribute(org.bson.Document valuesDoc, String fieldName, Class<T> clazz) {
        try {
            org.bson.Document fieldDoc = (org.bson.Document) valuesDoc.get(fieldName);
            if (fieldDoc == null) return null;

            T instance = clazz.getDeclaredConstructor().newInstance();

            // Set attributeId
            java.lang.reflect.Method setAttributeIdMethod = clazz.getMethod("setAttributeId", String.class);
            setAttributeIdMethod.invoke(instance, fieldDoc.getString("attributeId"));

            // Set content
            List<org.bson.Document> contentDocs = (List<org.bson.Document>) fieldDoc.get("content");
            if (contentDocs != null) {
                List<Content> contentList = new ArrayList<>();
                for (org.bson.Document contentDoc : contentDocs) {
                    Content content = new Content();
                    content.setValue(contentDoc.get("value"));
                    contentList.add(content);
                }

                java.lang.reflect.Method setContentMethod = clazz.getMethod("setContent", List.class);
                setContentMethod.invoke(instance, contentList);
            }

            return instance;

        } catch (Exception e) {
            log.debug("Failed to extract {} from document", fieldName, e);
            return null;
        }
    }

    /**
     * Custom data structure for ultra-fast invoice data retrieval
     */
    private static class InvoiceData {
        private String invoiceNumber;
        private List<InvoiceItem> items;
        private double grandTotal;

        // Getters and setters...
    }

    private static class InvoiceItem {
        private String partNumber;
        private String description;
        private int quantity;
        private double unitPrice;
        private double total;

        // Getters and setters...
    }

    // ==================== DEBUG METHODS ====================

    /**
     * Debug method to check what's actually in the database
     */
    private void debugDatabaseContent(String externalId) {
        try {
            log.info("=== DEBUG: Checking database content for externalId: {} ===", externalId);

            // Check if document exists with externalId
            Query query = new Query(Criteria.where("externalId").is(externalId));
            long count = mongoTemplate.count(query, "entity");
            log.info("DEBUG: Found {} documents with externalId: {}", count, externalId);

            if (count > 0) {
                // Get the document and log its structure
                org.bson.Document doc = mongoTemplate.findOne(query, org.bson.Document.class, "entity");
                if (doc != null) {
                    log.info("DEBUG: Document found with _id: {}", doc.getObjectId("_id"));
                    log.info("DEBUG: Document externalId: {}", doc.getString("externalId"));
                    log.info("DEBUG: Document parentId: {}", doc.getString("parentId"));
                    log.info("DEBUG: Document entityType: {}", doc.getString("entityType"));
                    log.info("DEBUG: Document has values: {}", doc.get("values") != null);
                    log.info("DEBUG: Document has complexValues: {}", doc.get("complexValues") != null);

                    // Log the full document structure (be careful in production)
                    log.debug("DEBUG: Full document: {}", doc.toJson());
                }
            } else {
                // Check if there are any documents in the collection
                long totalCount = mongoTemplate.count(new Query(), "entity");
                log.info("DEBUG: Total documents in 'entity' collection: {}", totalCount);

                // Check if there are documents with similar externalId
                Query similarQuery = new Query(Criteria.where("externalId").regex(".*" + externalId + ".*", "i"));
                long similarCount = mongoTemplate.count(similarQuery, "entity");
                log.info("DEBUG: Documents with similar externalId: {}", similarCount);

                if (similarCount > 0) {
                    List<org.bson.Document> similarDocs = mongoTemplate.find(similarQuery, org.bson.Document.class, "entity");
                    for (org.bson.Document doc : similarDocs) {
                        log.info("DEBUG: Similar document externalId: {}", doc.getString("externalId"));
                    }
                }
            }

        } catch (Exception e) {
            log.error("DEBUG: Error checking database content", e);
        }
    }

    // ==================== ORDER INFORMATION EXTRACTION METHODS ====================

    /**
     * Extract order date from Values array
     */
    private String extractOrderDate(List<com.atp.product.controller.dto.response.atomm_responses.Value> values) {
        if (values == null) return null;

        for (com.atp.product.controller.dto.response.atomm_responses.Value value : values) {
            if ("ECM_OrderDate".equals(value.getAttributeId())) {
                List<Content> content = value.getContent();
                if (content != null && !content.isEmpty()) {
                    Object dateValue = content.get(0).getValue();
                    if (dateValue != null) {
                        try {
                            // Handle different date formats
                            if (dateValue instanceof Date) {
                                SimpleDateFormat dateFormat = new SimpleDateFormat("dd MMM yyyy, HH:mm a");
                                return dateFormat.format((Date) dateValue);
                            } else if (dateValue instanceof String) {
                                // Try to parse the date string and reformat
                                return formatDateString((String) dateValue);
                            }
                        } catch (Exception e) {
                            log.debug("Failed to format order date: {}", dateValue, e);
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * Extract delivery method from Values array
     */
    private String extractDeliveryMethod(List<com.atp.product.controller.dto.response.atomm_responses.Value> values) {
        if (values == null) return null;

        for (com.atp.product.controller.dto.response.atomm_responses.Value value : values) {
            if ("DeliveryMethod".equals(value.getAttributeId())) {
                List<Content> content = value.getContent();
                if (content != null && !content.isEmpty()) {
                    Object methodValue = content.get(0).getValue();
                    return methodValue != null ? methodValue.toString() : null;
                }
            }
        }
        return null;
    }

    /**
     * Extract order status from Values array
     */
    private String extractOrderStatus(List<com.atp.product.controller.dto.response.atomm_responses.Value> values) {
        if (values == null) return null;

        for (com.atp.product.controller.dto.response.atomm_responses.Value value : values) {
            if ("OrderStatus".equals(value.getAttributeId())) {
                List<Content> content = value.getContent();
                if (content != null && !content.isEmpty()) {
                    Object statusValue = content.get(0).getValue();
                    return statusValue != null ? statusValue.toString() : null;
                }
            }
        }
        return null;
    }

    /**
     * Extract bin location from Values array
     */
    private String extractBinLocation(List<com.atp.product.controller.dto.response.atomm_responses.Value> values) {
        if (values == null) return null;

        for (com.atp.product.controller.dto.response.atomm_responses.Value value : values) {
            if ("BinLocation".equals(value.getAttributeId())) {
                List<Content> content = value.getContent();
                if (content != null && !content.isEmpty()) {
                    Object locationValue = content.get(0).getValue();
                    return locationValue != null ? locationValue.toString() : null;
                }
            }
        }
        return null;
    }

    /**
     * Extract PartsPurchaseOrderID from Values array
     */
    private String extractPartsPurchaseOrderId(List<com.atp.product.controller.dto.response.atomm_responses.Value> values) {
        if (values == null) return null;

        for (com.atp.product.controller.dto.response.atomm_responses.Value value : values) {
            if ("PartsPurchaseOrderID".equals(value.getAttributeId())) {
                List<Content> content = value.getContent();
                if (content != null && !content.isEmpty()) {
                    Object poIdValue = content.get(0).getValue();
                    return poIdValue != null ? poIdValue.toString() : null;
                }
            }
        }
        return "N/A";
    }

    /**
     * Format date string to readable format
     */
    private String formatDateString(String dateString) {
        try {
            // Try different date formats that might be in the database
            String[] possibleFormats = {
                "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
                "yyyy-MM-dd'T'HH:mm:ss'Z'",
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-dd",
                "dd/MM/yyyy",
                "MM/dd/yyyy"
            };

            SimpleDateFormat outputFormat = new SimpleDateFormat("dd MMM yyyy, HH:mm a");

            for (String format : possibleFormats) {
                try {
                    SimpleDateFormat inputFormat = new SimpleDateFormat(format);
                    Date date = inputFormat.parse(dateString);
                    return outputFormat.format(date);
                } catch (Exception e) {
                    // Try next format
                }
            }

            // If no format works, return the original string
            return dateString;

        } catch (Exception e) {
            log.debug("Failed to format date string: {}", dateString, e);
            return dateString;
        }
    }

    /**
     * Add company logo and title to the document
     */
    private void addCompanyLogoAndTitle(Document document) {
        try {
            // Create a table for logo and title layout
            Table headerTable = new Table(UnitValue.createPercentArray(new float[]{30, 70}))
                    .useAllAvailableWidth();

            // Try to add logo (you'll need to place the logo file in resources)
            try {
                // You can place your logo file in src/main/resources/static/images/
                // For now, I'll create a placeholder that you can replace with actual logo
                String logoPath = "static/images/action_Logo.png"; // Update this path to your logo

                // If logo file exists, add it
                java.net.URL logoUrl = getClass().getClassLoader().getResource(logoPath);
                if (logoUrl != null) {
                    Image logo = new Image(ImageDataFactory.create(logoUrl));
                    logo.setWidth(160); // Adjust size as needed
                    logo.setHeight(30);
                    headerTable.addCell(new Cell().add(logo).setBorder(Border.NO_BORDER));
                } else {
                    // Fallback: Company name if logo not found
                    headerTable.addCell(new Cell().add(new Paragraph("ATP Parts\nCompany")
                            .setBold()
                            .setFontSize(14)
                            .setTextAlignment(TextAlignment.LEFT))
                            .setBorder(Border.NO_BORDER));
                }
            } catch (Exception e) {
                log.debug("Could not load logo, using text fallback", e);
                // Fallback: Company name
                headerTable.addCell(new Cell().add(new Paragraph("ATP Parts\nCompany")
                        .setBold()
                        .setFontSize(14)
                        .setTextAlignment(TextAlignment.LEFT))
                        .setBorder(Border.NO_BORDER));
            }

            // Add INVOICE title
            headerTable.addCell(new Cell().add(new Paragraph("INVOICE")
                    .setBold()
                    .setFontSize(24)
                    .setTextAlignment(TextAlignment.RIGHT))
                    .setBorder(Border.NO_BORDER));

            document.add(headerTable);

        } catch (Exception e) {
            log.error("Error adding logo and title", e);
            // Fallback: Simple title
            document.add(new Paragraph("INVOICE")
                    .setBold()
                    .setFontSize(20)
                    .setTextAlignment(TextAlignment.CENTER));
        }
    }
}
