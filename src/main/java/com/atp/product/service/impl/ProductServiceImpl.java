package com.atp.product.service.impl;

import com.atp.product.controller.dto.response.ProductDetailsResponse;
import com.atp.product.controller.dto.response.ProductResponseHomePage;
import com.atp.product.model.Brands;
import com.atp.product.repository.BrandRepository;
import com.atp.product.service.ProductService;
import com.atp.product.utils.AuthorizationUtils;
import com.atp.product.utils.Constants;
import com.atp.product.exception.DomainException;
import jakarta.servlet.http.HttpServletRequest;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Service
public class ProductServiceImpl implements ProductService {

    public static final Logger logger = LoggerFactory.getLogger(ProductServiceImpl.class);
    private final MongoTemplate mongoTemplate;
    private final BrandRepository brandRepository;
    private final S3ServiceImpl s3Service;
    private final CommonServiceImpl commonServiceImpl;
    private final HttpServletRequest request;

    public ProductServiceImpl(MongoTemplate mongoTemplate, BrandRepository brandRepository, S3ServiceImpl s3Service, CommonServiceImpl commonServiceImpl, HttpServletRequest request) {
        this.mongoTemplate = mongoTemplate;
        this.brandRepository = brandRepository;
        this.s3Service = s3Service;
        this.commonServiceImpl = commonServiceImpl;
        this.request = request;
    }
    @Value("${aws.s3.bucket.name}")
    private String bucketName;

    @Value("${assets.root}")
    private String assetsRoot;

    public List<ProductResponseHomePage> getFeaturedProducts() {
        logger.info("Inside call to getFeaturedProducts() service");
        return getProducts("featuredItem");
    }

    public List<ProductResponseHomePage> getNewProducts() {
        logger.info("Inside call to getNewProducts() service");
        return getProducts("newItem");
    }

    private List<ProductResponseHomePage> getProducts(String itemType) {
        logger.info("Inside call to getProducts() method with itemType: {}", itemType);
        List<ProductResponseHomePage> results;
        try {
            List<AggregationOperation> operations = new ArrayList<>();
            MatchOperation matchOperation = Aggregation.match(Criteria.where(Constants.IS_DELETED).is(false)
                    .and(Constants.ACTIVE).is(true)
                    .and(itemType).is(true));
            operations.add(matchOperation);

            if(AuthorizationUtils.isAuthorizationHeaderValid(request)){
                String customerCorrelationId = AuthorizationUtils.getCustomerCorrelationId(request);
                operations.add(getLookupOperation(customerCorrelationId));
                operations.add(getAddFieldsOperation());
            }

            ProjectionOperation projectionOperation = getProjectionOperation();
            operations.add(projectionOperation);

            SortOperation sortOperation = new SortOperation(Sort.by(Sort.Direction.DESC, Constants.LAST_MODIFIED_DATE));
            operations.add(sortOperation);
            operations.add(Aggregation.limit(20));

            Aggregation aggregation = Aggregation.newAggregation(operations);
            logger.info("Aggregation pipeline getProducts(): {}", aggregation);

            results = mongoTemplate.aggregate(aggregation, Constants.PRODUCT_COLLECTION_NAME, ProductResponseHomePage.class)
                    .getMappedResults();
            if(!results.isEmpty()){
                results = commonServiceImpl.resizeImages(results);
            }
            logger.info("Aggregation pipeline results getProducts(): {}", results);
        } catch (IllegalArgumentException e) {
            throw new DomainException(Constants.INVALID_ARGUMENT_PROVIDED, e.getMessage());
        } catch (Exception e) {
            throw new DomainException("An error occurred while retrieving products by item type", e.getMessage());
        }
        return results;
    }

    public AggregationOperation getLookupOperation(String customerCorrelationId){
        try{
            Document lookupStage = new Document("$lookup", new Document()
                    .append("from", Constants.WISHLIST_COLLECTION_NAME)
                    .append("let", new Document(Constants.PART_NUMBER, "$"+Constants.PART_NUMBER))
                    .append("pipeline", List.of(
                            new Document("$match", new Document("$expr", new Document("$and", List.of(
                                    new Document("$eq", List.of("$customerCorrelationId", customerCorrelationId)),
                                    new Document("$in", List.of("$$"+Constants.PART_NUMBER, "$productDetails.partNumber"))
                            ))))
                    ))
                    .append("as", "wishlistEntry")
            );
            return commonServiceImpl.documentToAggregationOperation(lookupStage);
        } catch (Exception e) {
            throw new DomainException("An error occurred while retrieving products by item type", e.getMessage());
        }
    }
    public AggregationOperation getAddFieldsOperation(){
        try {
            Document addFieldsStage = new Document("$addFields", new Document(Constants.WISHLIST_FLAG,
                    new Document("$gt", List.of(
                            new Document("$size", "$wishlistEntry"), 0
                    ))
            ));
            return commonServiceImpl.documentToAggregationOperation(addFieldsStage);
        }
         catch (Exception e) {
            throw new DomainException("An error occurred while creating the addFields operation", e.getMessage());
        }
    }


    public ProjectionOperation getProjectionOperation(){
        try {
            return Aggregation.project()
                    .and(Constants.CATALOG_ID).as("productId")
                    .and(Constants.NAME).as("productName")
                    .and(Constants.CATEGORY).as(Constants.CATEGORY_NAME)
                    .and(Constants.PRIMARY_IMAGE).as(Constants.PRIMARY_IMAGE)
                    .and(Constants.LAST_MODIFIED_DATE).as(Constants.LAST_MODIFIED_DATE)
                    .and(Constants.PART_NUMBER).as(Constants.PART_NUMBER)
                    .and(Constants.PART_NUMBER_SLUG).as(Constants.PART_NUMBER_SLUG)
                    .and(Constants.ASSET_MAP).as(Constants.ASSET_MAP)
                    .and(Constants.DESCRIPTION).as(Constants.DESCRIPTION)
                    .and(Constants.WISHLIST_FLAG).as(Constants.WISHLIST_FLAG)
                    .and(Constants.PRICE).as(Constants.PRICE)
                    .and(Constants.VENDOR_NAME).as(Constants.VENDOR_NAME)
                    .and(Constants.CROSS_REFERENCE_ID).as(Constants.CROSS_REFERENCE_ID)
                    ;
        }
         catch (Exception e) {
            throw new DomainException("An error occurred while creating the projection operation", e.getMessage());
        }
    }
    /*@Override
    public ProductDetailsResponse getProductDetailsByPartNumberSlug(String partNumberSlug) {
        ProductDetailsResponse results;
        ProductDetailsResponse finalResult;
        try {
            List<AggregationOperation> operations = new ArrayList<>();

            MatchOperation matchOperation = Aggregation.match(Criteria.where(Constants.IS_DELETED).is(false)
                    .and(Constants.ACTIVE).is(true).and(Constants.PART_NUMBER_SLUG).is(partNumberSlug));
            operations.add(matchOperation);

            if(AuthorizationUtils.isAuthorizationHeaderValid(request)){
                String customerCorrelationId = AuthorizationUtils.getCustomerCorrelationId(request);
                operations.add(getLookupOperation(Constants.PART_NUMBER,"productDetails.partNumber","wishlistEntry",customerCorrelationId));
                operations.add(getAddFieldsOperation());
            }

            Aggregation aggregation = Aggregation.newAggregation(operations);
            logger.info("Aggregation pipeline getProductDetailsByPartNumber(): {}", aggregation);

            results = mongoTemplate.aggregate(aggregation, Constants.PRODUCT_COLLECTION_NAME, ProductDetailsResponse.class)
                    .getUniqueMappedResult();

            if (Objects.nonNull(results)) {
                finalResult = results;

                String primaryImage = results.getPrimaryImage();
                List<String> fullImages = new ArrayList<>();

                if (primaryImage != null && !primaryImage.isBlank()) {
                    fullImages.add(primaryImage);
                }
                if (results.getAlternateImages() != null && !results.getAlternateImages().isEmpty()) {
                    fullImages.addAll(results.getAlternateImages());
                }
                finalResult.setAlternateImages(fullImages);

                // Prepare maps for image URLs
                Map<String, String> assetMediumFullImage = new LinkedHashMap<>();
                Map<String, String> assetSmallMediumImage = new LinkedHashMap<>();

                // Process alternate images
                if (!fullImages.isEmpty()) {
                    for (String image : results.getAlternateImages()) {
                        String alternateImage = s3Service.generatePresignedUrl(bucketName, assetsRoot + "/" + image).toString();
                        String basePattern = image.substring(0, image.lastIndexOf('_')) + "_";

                        // Find matching asset URLs
                        for (Map.Entry<String, String> entry : results.getAssetMap().entrySet()) {
                            String url = entry.getValue();
                            if (url.startsWith(basePattern) && url.contains("400x400")) {
                                String mediumImageUrl = s3Service.generatePresignedUrl(bucketName, assetsRoot + "/" + url).toString();
                                assetMediumFullImage.put(mediumImageUrl, alternateImage);
                                if (Objects.equals(primaryImage, image))
                                    finalResult.setPrimaryImage(mediumImageUrl);

                                String smallImageUrl = getSmallImageUrl(results, basePattern);
                                assetSmallMediumImage.put(smallImageUrl, mediumImageUrl);

                                break;
                            }
                        }
                    }
                }
                Optional<Brands> brandDetails = brandRepository.findByBrandId(finalResult.getBrandId());
                if (brandDetails.isPresent()) {
                    finalResult.setBrandLogoUrl(s3Service.generatePresignedUrl(bucketName, assetsRoot + "/" + brandDetails.get().getBrandLogoUrl()).toString());
                }
                else
                    finalResult.setBrandLogoUrl("");
                finalResult.setAssetMediumFullImage(assetMediumFullImage);
                finalResult.setAssetSmallMediumImage(assetSmallMediumImage);

                //Set price and quantity in product
                if(AuthorizationUtils.isAuthorizationHeaderValid(request)){
                    Map<String, String> priceAndQuantity = commonServiceImpl.getPriceAndQuantityForProduct(finalResult.getPartNumber());
                    if (Objects.nonNull(priceAndQuantity) && !priceAndQuantity.isEmpty()){
                        finalResult.setPrice(Double.parseDouble(priceAndQuantity.get(Constants.PRICE)));
                        finalResult.setQuantity(Integer.parseInt(priceAndQuantity.get(Constants.QUANTITY)));
                    }
                }
            } else {
                throw new DomainException(Constants.INPUT_PARAMETER_NOT_FOUND, "Product not found for given part number slug");
            }

            logger.info("Aggregation pipeline results getProductDetailsByPartNumber(): {}", finalResult);
        } catch (IllegalArgumentException e) {
            throw new DomainException(Constants.INVALID_ARGUMENT_PROVIDED, e.getMessage());
        } catch (Exception e) {
            throw new DomainException("An error occurred while retrieving product details by partNumber", e.getMessage());
        }

        return finalResult; // Return finalResult instead of results
    }*/
    @Override
    public ProductDetailsResponse getProductDetailsByPartNumberSlug(String partNumberSlug) {
        try {
            List<AggregationOperation> operations = createAggregationOperations(partNumberSlug);
            Aggregation aggregation = Aggregation.newAggregation(operations);

            logger.info("Aggregation pipeline getProductDetailsByPartNumber(): {}", aggregation);
            ProductDetailsResponse results = mongoTemplate.aggregate(aggregation, Constants.PRODUCT_COLLECTION_NAME, ProductDetailsResponse.class)
                    .getUniqueMappedResult();

            if (Objects.isNull(results)) {
                throw new DomainException(Constants.INPUT_PARAMETER_NOT_FOUND, "Product not found for given part number slug");
            }

            return enrichProductDetails(results);
        } catch (IllegalArgumentException e) {
            throw new DomainException(Constants.INVALID_ARGUMENT_PROVIDED, e.getMessage());
        } catch (Exception e) {
            throw new DomainException("An error occurred while retrieving product details by partNumber", e.getMessage());
        }
    }

    private List<AggregationOperation> createAggregationOperations(String partNumberSlug) {
        List<AggregationOperation> operations = new ArrayList<>();
        operations.add(Aggregation.match(Criteria.where(Constants.IS_DELETED).is(false)
                .and(Constants.ACTIVE).is(true)
                .and(Constants.PART_NUMBER_SLUG).is(partNumberSlug)));

        if (AuthorizationUtils.isAuthorizationHeaderValid(request)) {
            String customerCorrelationId = AuthorizationUtils.getCustomerCorrelationId(request);
            operations.add(getLookupOperation(customerCorrelationId));
            operations.add(getAddFieldsOperation());
        }
        return operations;
    }

    private ProductDetailsResponse enrichProductDetails(ProductDetailsResponse result) {
        String primaryImage = result.getPrimaryImage();
        List<String> fullImages = getFullImages(result, primaryImage);

        result.setAlternateImages(fullImages);

        Map<String, String> assetMediumFullImage = new LinkedHashMap<>();
        Map<String, String> assetSmallMediumImage = new LinkedHashMap<>();

        processAlternateImages(result, fullImages, assetMediumFullImage, assetSmallMediumImage);

        result.setBrandLogoUrl(getBrandLogoUrl(result.getBrandId()));
        result.setAssetMediumFullImage(assetMediumFullImage);
        result.setAssetSmallMediumImage(assetSmallMediumImage);

        if (AuthorizationUtils.isAuthorizationHeaderValid(request)) {
            setPriceAndQuantity(result);
        }

        logger.info("Aggregation pipeline results getProductDetailsByPartNumber(): {}", result);
        return result;
    }

    private List<String> getFullImages(ProductDetailsResponse result, String primaryImage) {
        List<String> fullImages = new ArrayList<>();
        if (primaryImage != null && !primaryImage.isBlank()) {
            fullImages.add(primaryImage);
        }
        if (result.getAlternateImages() != null && !result.getAlternateImages().isEmpty()) {
            fullImages.addAll(result.getAlternateImages());
        }
        return fullImages;
    }

    private void processAlternateImages(ProductDetailsResponse result, List<String> fullImages,
                                        Map<String, String> assetMediumFullImage, Map<String, String> assetSmallMediumImage) {
        if (!fullImages.isEmpty()) {
            for (String image : result.getAlternateImages()) {
                String alternateImage = s3Service.generatePresignedUrl(bucketName, assetsRoot + "/" + image).toString();
                String basePattern = image.substring(0, image.lastIndexOf('_')) + "_";

                findMatchingAssetUrls(result, image, result.getPrimaryImage(), assetMediumFullImage, assetSmallMediumImage, alternateImage, basePattern);
            }
        }
    }

    private void findMatchingAssetUrls(ProductDetailsResponse result, String image, String primaryImage,
                                       Map<String, String> assetMediumFullImage, Map<String, String> assetSmallMediumImage,
                                       String alternateImage, String basePattern) {
        for (Map.Entry<String, String> entry : result.getAssetMap().entrySet()) {
            String url = entry.getValue();
            if (url.startsWith(basePattern) && url.contains("400x400")) {
                String mediumImageUrl = s3Service.generatePresignedUrl(bucketName, assetsRoot + "/" + url).toString();
                assetMediumFullImage.put(mediumImageUrl, alternateImage);

                if (Objects.equals(primaryImage, image)) {
                    result.setPrimaryImage(mediumImageUrl);
                }

                String smallImageUrl = getSmallImageUrl(result, basePattern);
                assetSmallMediumImage.put(smallImageUrl, mediumImageUrl);

                break;
            }
        }
    }

    private String getBrandLogoUrl(String brandId) {
        Optional<Brands> brandDetails = brandRepository.findByBrandId(brandId);
        return brandDetails.map(brand -> s3Service.generatePresignedUrl(bucketName, assetsRoot + "/" + brand.getBrandLogoUrl()).toString())
                .orElse("");
    }

    private void setPriceAndQuantity(ProductDetailsResponse result) {
       //Map<String, String> priceAndQuantity = commonServiceImpl.getPriceAndQuantityForProduct(result.getPartNumber());
       Map<String, String> priceAndQuantity = commonServiceImpl.getPriceAndQuantityForProductAsync(result.getPartNumber()).join();
        if (Objects.nonNull(priceAndQuantity) && !priceAndQuantity.isEmpty()) {
            // Use BigDecimal to parse and round the price
            BigDecimal price = new BigDecimal(priceAndQuantity.get(Constants.PRICE));
            price = price.setScale(2, RoundingMode.HALF_UP);

            // Set the price and quantity
            result.setPrice(price.doubleValue());
            result.setQuantity(Integer.parseInt(priceAndQuantity.get(Constants.QUANTITY)));
            result.setKarmakPartId(Integer.parseInt(priceAndQuantity.get(Constants.PARTS_INVENTORY_DETAIL_ID)));
            result.setBranch(priceAndQuantity.get(Constants.BRANCH_CODE));

           /* result.setPrice(Double.parseDouble(priceAndQuantity.get(Constants.PRICE)));
            result.setQuantity(Integer.parseInt(priceAndQuantity.get(Constants.QUANTITY)));*/
        }else {
            result.setPrice(0.0);
            result.setQuantity(0);
        }
    }


    private String getSmallImageUrl(ProductDetailsResponse results, String basePattern) {
        String smallImageUrl = null;
        if (results.getAssetMap() != null) {
            for (Map.Entry<String, String> entry : results.getAssetMap().entrySet()) {
                String url = entry.getValue();
                if (url.startsWith(basePattern) && url.contains("200x200")) {
                    smallImageUrl = s3Service.generatePresignedUrl(bucketName, assetsRoot + "/" + url).toString();
                    break;
                }
            }
        }
        return smallImageUrl;
    }
    @Override
    public List<ProductResponseHomePage> getSimilarProductListByCrossReferenceId(List<String> crossReferenceIds) {
        logger.info("Inside call to getSimilarProductListByCrossReferenceId() service with crossReferenceIds: {}", crossReferenceIds);
        List<ProductResponseHomePage> results;
        try {
            List<AggregationOperation> operations = new ArrayList<>();

            MatchOperation matchOperation = Aggregation.match(Criteria.where(Constants.IS_DELETED).is(false)
                    .and(Constants.ACTIVE).is(true)
                    .and(Constants.CATALOG_ID).in(crossReferenceIds));
            operations.add(matchOperation);

            if(AuthorizationUtils.isAuthorizationHeaderValid(request)){
                String customerCorrelationId = AuthorizationUtils.getCustomerCorrelationId(request);
                operations.add(getLookupOperation(customerCorrelationId));
                operations.add(getAddFieldsOperation());
            }

            ProjectionOperation projectionOperation = getProjectionOperation();
            operations.add(projectionOperation);

            SortOperation sortOperation = new SortOperation(Sort.by(Sort.Direction.DESC, Constants.LAST_MODIFIED_DATE));
            operations.add(sortOperation);
            operations.add(Aggregation.limit(20));

            Aggregation aggregation = Aggregation.newAggregation(operations);
            logger.info("Aggregation pipeline getProducts(): {}", aggregation);

            results = mongoTemplate.aggregate(aggregation, Constants.PRODUCT_COLLECTION_NAME, ProductResponseHomePage.class).getMappedResults();
            logger.info("Aggregation pipeline results getSimilarProductListByBrandCategorySlug(): {}", results);
            if(!results.isEmpty()){
                results = commonServiceImpl.resizeImages(results);
            }
        }
        catch (IllegalArgumentException e) {
            throw new DomainException(Constants.INVALID_ARGUMENT_PROVIDED, e);
        }
        catch (Exception e) {
            throw new DomainException("An error occurred while retrieving similar products by brand slug and category slug", e.getMessage());
        }
        return results;
    }
}
