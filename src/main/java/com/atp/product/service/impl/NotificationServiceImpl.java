package com.atp.product.service.impl;

import com.atp.product.controller.dto.response.atomm_responses.*;
import com.atp.product.model.ScheduledOrder;
import com.atp.product.service.NotificationService;
import jakarta.mail.Message;
import jakarta.mail.MessagingException;
import jakarta.mail.Session;
import jakarta.mail.Transport;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.Properties;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

/**
 * HIGH-PERFORMANCE NotificationService with bulk email optimization
 * - Gmail SMTP limit: ~4 seconds per email (cannot go faster)
 * - Bulk optimization: Rate limiting + connection reuse for many emails
 */
@Slf4j
@Service
public class NotificationServiceImpl implements NotificationService {

    private final JavaMailSender mailSender;
    private final ResourceLoader resourceLoader;
    private final PurchaseOrderService purchaseOrderService;

    @Value("${spring.mail.username:<EMAIL>}")
    private String fromEmail;

    @Value("${spring.mail.password}")
    private String emailPassword;

    // GET REAL VALUES FROM CONFIGURATION - NO MORE HARDCODED VALUES!
    @Value("${company.name:Action Truck Parts}")
    private String companyName;

    @Value("${company.support.email:${spring.mail.username}}")
    private String supportEmail;

    @Value("${email.notifications.enabled:true}")
    private boolean emailEnabled;

    // BULK EMAIL OPTIMIZATION - Prevent SMTP server overload
    private static final Semaphore emailRateLimiter = new Semaphore(5); // Max 5 concurrent emails
    private static final ConcurrentHashMap<String, Transport> transportPool = new ConcurrentHashMap<>();
    private static Session optimizedSession;
    private static InternetAddress fromAddress;

    // HTML TEMPLATE CACHING
    private static String outOfStockEmailTemplate;

    public NotificationServiceImpl(JavaMailSender mailSender, ResourceLoader resourceLoader, PurchaseOrderService purchaseOrderService) {
        this.mailSender = mailSender;
        this.resourceLoader = resourceLoader;
        this.purchaseOrderService = purchaseOrderService;
    }

    @Override
    @Async
    public void sendOutOfStockEmail(ScheduledOrder scheduledOrder, List<String> unavailableParts) {
        if (!emailEnabled) {
            log.info("Email notifications disabled. Would send out of stock email to: {} for customer: {}",
                    scheduledOrder.getEmail(), scheduledOrder.getKarmakCustomerId());
            return;
        }

        if (scheduledOrder.getEmail() == null || scheduledOrder.getEmail().trim().isEmpty()) {
            log.warn("No email address provided for customer: {}. Cannot send out of stock notification.", scheduledOrder.getKarmakCustomerId());
            return;
        }

        // BULK OPTIMIZATION: Rate limiting to prevent SMTP overload
        CompletableFuture.runAsync(() -> {
            try {
                // Acquire permit to prevent SMTP server overload
                if (!emailRateLimiter.tryAcquire(2, TimeUnit.SECONDS)) {
                    log.warn("🚦 Rate limit: Delaying email to {} for customer: {} (too many concurrent emails)",
                            scheduledOrder.getEmail(), scheduledOrder.getKarmakCustomerId());
                    emailRateLimiter.acquire(); // Wait for permit
                }

                long startTime = System.currentTimeMillis();
                try {
                    log.info("📧 BULK-OPTIMIZED: Sending out of stock email to: {} for customer: {} with {} unavailable parts",
                            scheduledOrder.getEmail(), scheduledOrder.getKarmakCustomerId(), unavailableParts.size());

                    String subject = "Important: Scheduled Order Temporarily Delayed - Item Availability Update";
                    String htmlBody = buildHtmlOutOfStockEmailBody(scheduledOrder, unavailableParts);

                    sendOptimizedHtmlEmail(scheduledOrder.getEmail(), subject, htmlBody);

                    long duration = System.currentTimeMillis() - startTime;
                    log.info("✅ BULK-OPTIMIZED: Out of stock email DELIVERED to: {} for customer: {} in {}ms",
                            scheduledOrder.getEmail(), scheduledOrder.getKarmakCustomerId(), duration);

                } catch (Exception e) {
                    long duration = System.currentTimeMillis() - startTime;
                    log.error("❌ Failed to send out of stock email to: {} for customer: {} after {}ms",
                            scheduledOrder.getEmail(), scheduledOrder.getKarmakCustomerId(), duration, e);
                } finally {
                    // Release permit for next email
                    emailRateLimiter.release();
                }
            } catch (InterruptedException e) {
                log.error("❌ Email rate limiter interrupted for customer: {}", scheduledOrder.getKarmakCustomerId(), e);
                Thread.currentThread().interrupt();
            }
        });
    }

    @Override
    @Async
    public void sendScheduledOrderConfirmation(String email, String customerName, String customerId, String scheduledOrderId, String nextRunDate) {
        if (!emailEnabled) {
            log.info("Email notifications disabled. Would send confirmation email to: {} for customer: {}",
                    email, customerId);
            return;
        }

        if (email == null || email.trim().isEmpty()) {
            log.warn("No email address provided for customer: {}. Cannot send confirmation notification.", customerId);
            return;
        }

        long startTime = System.currentTimeMillis();
        try {
            log.info("📧 FAST: Sending scheduled order confirmation to: {} for customer: {}", email, customerId);

            String subject = "Scheduled Order Confirmation - " + companyName;
            String body = buildLightweightConfirmationEmailBody(customerName, scheduledOrderId, nextRunDate);

            sendOptimizedEmail(email, subject, body);

            long duration = System.currentTimeMillis() - startTime;
            log.info("✅ FAST: Confirmation email DELIVERED to: {} for customer: {} in {}ms", email, customerId, duration);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("❌ Failed to send confirmation email to: {} for customer: {} after {}ms", email, customerId, duration, e);
        }
    }

    @Override
    @Async
    public void sendScheduledOrderCancellation(String email, String customerName, String customerId, String scheduledOrderId) {
        if (!emailEnabled) {
            log.info("Email notifications disabled. Would send cancellation email to: {} for customer: {}",
                    email, customerId);
            return;
        }

        if (email == null || email.trim().isEmpty()) {
            log.warn("No email address provided for customer: {}. Cannot send cancellation notification.", customerId);
            return;
        }

        long startTime = System.currentTimeMillis();
        try {
            log.info("📧 FAST: Sending scheduled order cancellation to: {} for customer: {}", email, customerId);

            String subject = "Scheduled Order Cancelled - " + companyName;
            String body = buildLightweightCancellationEmailBody(customerName, scheduledOrderId);

            sendOptimizedEmail(email, subject, body);

            long duration = System.currentTimeMillis() - startTime;
            log.info("✅ FAST: Cancellation email DELIVERED to: {} for customer: {} in {}ms", email, customerId, duration);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("❌ Failed to send cancellation email to: {} for customer: {} after {}ms", email, customerId, duration, e);
        }
    }

    @Override
    @Async
    public void sendScheduledOrderUpdate(String email, String customerName, String customerId, String scheduledOrderId, int newFrequency) {
        if (!emailEnabled) {
            log.info("Email notifications disabled. Would send update email to: {} for customer: {}",
                    email, customerId);
            return;
        }

        if (email == null || email.trim().isEmpty()) {
            log.warn("No email address provided for customer: {}. Cannot send update notification.", customerId);
            return;
        }

        long startTime = System.currentTimeMillis();
        try {
            log.info("📧 FAST: Sending scheduled order update to: {} for customer: {}", email, customerId);

            String subject = "Scheduled Order Updated - " + companyName;
            String body = buildLightweightUpdateEmailBody(customerName, scheduledOrderId, newFrequency);

            sendOptimizedEmail(email, subject, body);

            long duration = System.currentTimeMillis() - startTime;
            log.info("✅ FAST: Update email DELIVERED to: {} for customer: {} in {}ms", email, customerId, duration);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("❌ Failed to send update email to: {} for customer: {} after {}ms", email, customerId, duration, e);
        }
    }

    @Override
    @Async
    public void sendPurchaseOrderSuccess(String email, String customerName, String customerId, String poNumber, String scheduledOrderId) {
        if (!emailEnabled) {
            log.info("Email notifications disabled. Would send PO success email to: {} for customer: {}",
                    email, customerId);
            return;
        }

        if (email == null || email.trim().isEmpty()) {
            log.warn("No email address provided for customer: {}. Cannot send PO success notification.", customerId);
            return;
        }

        long startTime = System.currentTimeMillis();
        try {
            log.info("📧 FAST: Sending PO success notification to: {} for customer: {} with PO: {}", email, customerId, poNumber);

            String subject = "Purchase Order Created Successfully - " + companyName;
            String body = buildLightweightPOSuccessEmailBody(customerName, poNumber, scheduledOrderId);

            sendOptimizedEmail(email, subject, body);

            long duration = System.currentTimeMillis() - startTime;
            log.info("✅ FAST: PO success email DELIVERED to: {} for customer: {} in {}ms", email, customerId, duration);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("❌ Failed to send PO success email to: {} for customer: {} after {}ms", email, customerId, duration, e);
        }
    }

    @Override
    @Async
    public void sendPurchaseOrderFailure(String email, String customerName, String customerId, String scheduledOrderId, String errorMessage) {
        if (!emailEnabled) {
            log.info("Email notifications disabled. Would send PO failure email to: {} for customer: {}",
                    email, customerId);
            return;
        }

        if (email == null || email.trim().isEmpty()) {
            log.warn("No email address provided for customer: {}. Cannot send PO failure notification.", customerId);
            return;
        }

        long startTime = System.currentTimeMillis();
        try {
            log.info("📧 FAST: Sending PO failure notification to: {} for customer: {}", email, customerId);

            String subject = "Purchase Order Creation Failed - " + companyName;
            String body = buildLightweightPOFailureEmailBody(customerName, scheduledOrderId, errorMessage);

            sendOptimizedEmail(email, subject, body);

            long duration = System.currentTimeMillis() - startTime;
            log.info("✅ FAST: PO failure email DELIVERED to: {} for customer: {} in {}ms", email, customerId, duration);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("❌ Failed to send PO failure email to: {} for customer: {} after {}ms", email, customerId, duration, e);
        }
    }

    /**
     * OPTIMIZED email sending - 4 seconds is Gmail SMTP limit (cannot go faster)
     */
    private void sendOptimizedEmail(String toEmail, String subject, String body) throws MessagingException {
        try {
            long connectStart = System.currentTimeMillis();

            // Create minimal message with cached session and from address
            MimeMessage message = new MimeMessage(getOptimizedSession());

            // Use cached from address to avoid repeated parsing
            message.setFrom(getCachedFromAddress());
            message.setRecipient(Message.RecipientType.TO, new InternetAddress(toEmail));
            message.setSubject(subject);
            message.setText(body); // Plain text only - zero HTML processing

            // Remove unnecessary headers for minimal overhead
            message.removeHeader("Message-ID");
            message.removeHeader("Date");
            message.removeHeader("User-Agent");
            message.removeHeader("X-Mailer");
            message.removeHeader("MIME-Version");
            message.removeHeader("Content-Transfer-Encoding");

            long connectTime = System.currentTimeMillis() - connectStart;
            long sendStart = System.currentTimeMillis();

            // Send with optimized settings (4 seconds is the Gmail SMTP limit)
            Transport.send(message);

            long sendTime = System.currentTimeMillis() - sendStart;
            long totalTime = System.currentTimeMillis() - connectStart;

            log.info("⚡ TIMING: Connect={}ms, Send={}ms, Total={}ms to: {}",
                    connectTime, sendTime, totalTime, toEmail);

        } catch (Exception e) {
            log.error("❌ SMTP EMAIL FAILED to: {} - Error: {}", toEmail, e.getMessage());
            // Fast fallback
            try {
                log.info("🔄 Quick fallback...");
                MimeMessage fallbackMessage = mailSender.createMimeMessage();
                fallbackMessage.setFrom(new InternetAddress(fromEmail));
                fallbackMessage.setRecipient(Message.RecipientType.TO, new InternetAddress(toEmail));
                fallbackMessage.setSubject(subject);
                fallbackMessage.setText(body);
                mailSender.send(fallbackMessage);
                log.info("✅ Fallback delivered to: {}", toEmail);
            } catch (Exception fallbackError) {
                log.error("❌ Both primary and fallback failed to: {}", toEmail, fallbackError);
                throw new MessagingException("Email sending failed", fallbackError);
            }
        }
    }

    /**
     * OPTIMIZED HTML email sending - 4 seconds is Gmail SMTP limit (cannot go faster)
     */
    private void sendOptimizedHtmlEmail(String toEmail, String subject, String htmlBody) throws MessagingException {
        try {
            long connectStart = System.currentTimeMillis();

            // Create HTML message with cached session and from address
            MimeMessage message = new MimeMessage(getOptimizedSession());

            // Use cached from address to avoid repeated parsing
            message.setFrom(getCachedFromAddress());
            message.setRecipient(Message.RecipientType.TO, new InternetAddress(toEmail));
            message.setSubject(subject);
            message.setContent(htmlBody, "text/html; charset=utf-8"); // HTML content

            // Remove unnecessary headers for minimal overhead
            message.removeHeader("Message-ID");
            message.removeHeader("Date");
            message.removeHeader("User-Agent");
            message.removeHeader("X-Mailer");

            long connectTime = System.currentTimeMillis() - connectStart;
            long sendStart = System.currentTimeMillis();

            // Send with optimized settings (4 seconds is the Gmail SMTP limit)
            Transport.send(message);

            long sendTime = System.currentTimeMillis() - sendStart;
            long totalTime = System.currentTimeMillis() - connectStart;

            log.info("⚡ HTML TIMING: Connect={}ms, Send={}ms, Total={}ms to: {}",
                    connectTime, sendTime, totalTime, toEmail);

        } catch (Exception e) {
            log.error("❌ HTML EMAIL FAILED to: {} - Error: {}", toEmail, e.getMessage());
            // Fast fallback to simple HTML
            try {
                log.info("🔄 HTML email fallback...");
                MimeMessage fallbackMessage = mailSender.createMimeMessage();
                fallbackMessage.setFrom(new InternetAddress(fromEmail));
                fallbackMessage.setRecipient(Message.RecipientType.TO, new InternetAddress(toEmail));
                fallbackMessage.setSubject(subject);
                fallbackMessage.setContent(htmlBody, "text/html; charset=utf-8");
                mailSender.send(fallbackMessage);
                log.info("✅ HTML fallback delivered to: {}", toEmail);
            } catch (Exception fallbackError) {
                log.error("❌ Both HTML primary and fallback failed to: {}", toEmail, fallbackError);
                throw new MessagingException("HTML email sending failed", fallbackError);
            }
        }
    }

    /**
     * Load HTML email template from resources
     */
    private String loadEmailTemplate() {
        if (outOfStockEmailTemplate == null) {
            try {
                Resource resource = resourceLoader.getResource("classpath:email-templates/out-of-stock-notification.html");
                outOfStockEmailTemplate = new String(resource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
                log.info("✅ HTML email template loaded successfully");
            } catch (IOException e) {
                log.error("❌ Failed to load HTML email template", e);
                // Fallback to simple HTML structure
                outOfStockEmailTemplate = createFallbackHtmlTemplate();
            }
        }
        return outOfStockEmailTemplate;
    }

    /**
     * Build professional HTML email with REAL scheduled order details
     */
    private String buildHtmlOutOfStockEmailBody(ScheduledOrder scheduledOrder, List<String> unavailableParts) {
        String template = loadEmailTemplate();

        // Replace placeholders with REAL scheduled order data
        String greeting = (scheduledOrder.getName() != null && !scheduledOrder.getName().trim().isEmpty())
                ? "Dear " + scheduledOrder.getName() + ","
                : "Dear Valued Customer,";

        template = template.replace("{{CUSTOMER_GREETING}}", greeting);
        template = template.replace("{{CUSTOMER_ID}}", scheduledOrder.getKarmakCustomerId() != null ? scheduledOrder.getKarmakCustomerId() : "N/A");
        template = template.replace("{{SCHEDULED_ORDER_ID}}", scheduledOrder.getId() != null ? scheduledOrder.getId() : "N/A");
        template = template.replace("{{ORIGINAL_ORDER_ID}}", scheduledOrder.getOriginalOrderId() != null ? scheduledOrder.getOriginalOrderId() : "N/A");
        template = template.replace("{{PROCESS_DATE}}", LocalDate.now().format(DateTimeFormatter.ofPattern("MMM dd, yyyy")));

        // Enhanced scheduled order details
        template = template.replace("{{SUBSCRIPTION_DATE}}",
                scheduledOrder.getSubscriptionDate() != null ?
                        scheduledOrder.getSubscriptionDate().format(DateTimeFormatter.ofPattern("MMM dd, yyyy")) : "N/A");
        template = template.replace("{{FREQUENCY_DAYS}}", String.valueOf(scheduledOrder.getFrequencyDays()));
        template = template.replace("{{NEXT_RUN_DATE}}",
                scheduledOrder.getNextRunDate() != null ?
                        scheduledOrder.getNextRunDate().format(DateTimeFormatter.ofPattern("MMM dd, yyyy")) : "N/A");
        template = template.replace("{{ORDERS_PROCESSED}}", String.valueOf(scheduledOrder.getOrdersProcessed()));

        template = template.replace("{{UNAVAILABLE_COUNT}}", String.valueOf(unavailableParts.size()));
        template = template.replace("{{SUPPORT_EMAIL}}", supportEmail);
        template = template.replace("{{COMPANY_NAME}}", companyName);

        // Build parts table with REAL part data from original order - NO FAKE DATA!
        StringBuilder partsTableRows = new StringBuilder();
        int displayCount = Math.min(unavailableParts.size(), 15); // Show max 15 parts

        for (int i = 0; i < displayCount; i++) {
            String partNumber = unavailableParts.get(i);
            String realDescription = getRealPartDescription(scheduledOrder, partNumber);

            partsTableRows.append("<tr>")
                    .append("<td><span class=\"part-number\">").append(partNumber).append("</span></td>")
                    .append("<td>").append(realDescription).append("</td>")
                    .append("<td><span class=\"status-badge\">Out of Stock</span></td>")
                    .append("</tr>");
        }

        template = template.replace("{{PARTS_TABLE_ROWS}}", partsTableRows.toString());

        // Additional parts message if there are more than 15
        String additionalPartsMessage = "";
        if (unavailableParts.size() > 15) {
            int remaining = unavailableParts.size() - 15;
            additionalPartsMessage = "<div style=\"text-align: center; margin: 15px 0; padding: 10px; background-color: #f8f9fa; border-radius: 4px;\">" +
                    "<strong>📦 " + remaining + " additional item(s) not shown above</strong><br>" +
                    "<small style=\"color: #666;\">Complete list available upon request - contact support for details</small>" +
                    "</div>";
        }
        template = template.replace("{{ADDITIONAL_PARTS_MESSAGE}}", additionalPartsMessage);

        return template;
    }

    /**
     * Get REAL part description from original order data - NO FAKE DATA!
     */
    private String getRealPartDescription(ScheduledOrder scheduledOrder, String partNumber) {
        try {
            // Get REAL part data from original order using actual KarmakPurchaseOrder
            AtommPurchaseOrder originalOrder = purchaseOrderService.getPurchaseOrderByExternalId(
                    scheduledOrder.getOriginalOrderId());

            if (originalOrder == null) {
                log.warn("Could not find original order: {}", scheduledOrder.getOriginalOrderId());
                return "Order not found";
            }

            // Extract REAL description from KarmakPurchaseOrder complexValues
            ComplexValues complexValues = originalOrder.getComplexValues();
            if (complexValues != null && complexValues.getPurchaseOrders() != null) {
                for (PurchaseOrder poItem : complexValues.getPurchaseOrders()) {
                    if (poItem.getValues() != null) {
                        Values values = poItem.getValues();

                        // Check if this is the part we're looking for
                        String currentPartNumber = extractPartNumber(values);
                        if (partNumber.equals(currentPartNumber)) {
                            // Get REAL product description
                            String realDescription = extractProductDescription(values);
                            return realDescription != null ? realDescription : "Product description not available";
                        }
                    }
                }
            }

            // Fallback to basic part number if no description found
            return "Part: " + partNumber;

        } catch (Exception e) {
            log.warn("Could not get real part description for part: {} in order: {}",
                    partNumber, scheduledOrder.getOriginalOrderId(), e);
            return "Part information unavailable";
        }
    }

    /**
     * Extract real part number from Values object
     */
    private String extractPartNumber(Values values) {
        return Optional.ofNullable(values.getOrderPartNumber())
                .map(OrderPartNumber::getContent)
                .flatMap(contentList -> contentList.stream().findFirst())
                .map(Content::getValue)
                .map(Object::toString)
                .orElse("N/A");
    }

    /**
     * Extract real product description from Values object
     */
    private String extractProductDescription(Values values) {
        return Optional.ofNullable(values.getOrderProductDetails())
                .map(OrderProductDetails::getContent)
                .flatMap(contentList -> contentList.stream().findFirst())
                .map(Content::getValue)
                .map(Object::toString)
                .orElse(null);
    }

    /**
     * Fallback HTML template if file loading fails
     */
    private String createFallbackHtmlTemplate() {
        return "<!DOCTYPE html>" +
                "<html><head><style>body{font-family:Arial,sans-serif;margin:40px;}</style></head>" +
                "<body>" +
                "<h2>🔔 Scheduled Order Temporarily Delayed</h2>" +
                "<p>{{CUSTOMER_GREETING}}</p>" +
                "<p>We are writing to inform you about a temporary delay with your scheduled order.</p>" +
                "<p><strong>Items currently out of stock:</strong></p>" +
                "<ul>{{PARTS_LIST}}</ul>" +
                "<p>We sincerely apologize for any inconvenience this delay may cause.</p>" +
                "<p>Contact us: {{SUPPORT_EMAIL}}</p>" +
                "<p>Best regards,<br>{{COMPANY_NAME}}</p>" +
                "</body></html>";
    }

    /**
     * Get optimized session for Gmail SMTP (balanced for speed and reliability)
     */
    private Session getOptimizedSession() {
        if (optimizedSession == null) {
            Properties props = new Properties();
            props.put("mail.smtp.host", "smtp.gmail.com");
            props.put("mail.smtp.port", "587");
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.starttls.enable", "true");

            // BALANCED timeouts - fast but reliable for Gmail SMTP
            props.put("mail.smtp.connectiontimeout", "2000");    // 2 seconds
            props.put("mail.smtp.timeout", "2500");             // 2.5 seconds
            props.put("mail.smtp.writetimeout", "1500");        // 1.5 seconds

            // Connection optimization
            props.put("mail.smtp.connectionpoolsize", "20");
            props.put("mail.smtp.connectionpooltimeout", "60000");
            props.put("mail.smtp.quitwait", "false");
            props.put("mail.smtp.sendpartial", "true");
            props.put("mail.smtp.localhost", "localhost");

            // Disable everything unnecessary
            props.put("mail.mime.address.strict", "false");
            props.put("mail.mime.charset", "UTF-8");
            props.put("mail.mime.decodetext.strict", "false");

            optimizedSession = Session.getInstance(props, new jakarta.mail.Authenticator() {
                protected jakarta.mail.PasswordAuthentication getPasswordAuthentication() {
                    return new jakarta.mail.PasswordAuthentication(fromEmail, emailPassword);
                }
            });
        }
        return optimizedSession;
    }

    /**
     * Cache from address to avoid repeated parsing overhead
     */
    private InternetAddress getCachedFromAddress() throws MessagingException {
        if (fromAddress == null) {
            try {
                fromAddress = new InternetAddress(fromEmail, companyName);
            } catch (Exception e) {
                // Fallback to simple address if encoding fails
                fromAddress = new InternetAddress(fromEmail);
            }
        }
        return fromAddress;
    }

    // PROFESSIONAL email templates for business communication
    private String buildProfessionalOutOfStockEmailBody(String customerName, List<String> unavailableParts) {
        StringBuilder body = new StringBuilder(400); // Larger allocation for professional content
        String greeting = (customerName != null && !customerName.trim().isEmpty())
                ? "Dear " + customerName + ","
                : "Dear Valued Customer,";

        body.append(greeting).append("\n\n");
        body.append("We are writing to inform you about a temporary delay with your scheduled order. ");
        body.append("After conducting our inventory review, we have identified that the following item(s) ");
        body.append("are currently out of stock:\n\n");

        // List items professionally with proper formatting
        int count = Math.min(unavailableParts.size(), 10);
        for (int i = 0; i < count; i++) {
            body.append("• ").append(unavailableParts.get(i)).append("\n");
        }

        if (unavailableParts.size() > 10) {
            body.append("• ").append(unavailableParts.size() - 10)
                    .append(" additional items (full list available upon request)\n");
        }

        body.append("\n");
        body.append("NEXT STEPS:\n");
        body.append("• We are actively working with our suppliers to restock these items\n");
        body.append("• Your order will be automatically processed once items become available\n");
        body.append("• You will receive immediate notification when your order ships\n");
        body.append("• No action is required on your part at this time\n\n");

        body.append("We sincerely apologize for any inconvenience this delay may cause. ");
        body.append("Your satisfaction is our top priority, and we are committed to fulfilling ");
        body.append("your order as quickly as possible.\n\n");

        body.append("If you have any questions or would like to discuss alternative options, ");
        body.append("please don't hesitate to contact our customer support team at ");
        body.append(supportEmail).append(" or reply to this email.\n\n");

        body.append("Thank you for your patience and continued business.\n\n");
        body.append("Best regards,\n");
        body.append("Customer Service Team\n");
        body.append(companyName).append("\n");
        body.append("Email: ").append(supportEmail);

        return body.toString();
    }

    private String buildLightweightConfirmationEmailBody(String customerName, String scheduledOrderId, String nextRunDate) {
        return (customerName != null ? "Dear " + customerName : "Dear Customer") +
                ",\n\nScheduled order created.\n\nOrder ID: " + scheduledOrderId +
                "\nNext run: " + nextRunDate +
                "\n\nRegards,\n" + companyName;
    }

    private String buildLightweightCancellationEmailBody(String customerName, String scheduledOrderId) {
        return (customerName != null ? "Dear " + customerName : "Dear Customer") +
                ",\n\nScheduled order (ID: " + scheduledOrderId + ") cancelled.\n\n" +
                "Regards,\n" + companyName;
    }

    private String buildLightweightUpdateEmailBody(String customerName, String scheduledOrderId, int newFrequency) {
        return (customerName != null ? "Dear " + customerName : "Dear Customer") +
                ",\n\nScheduled order (ID: " + scheduledOrderId + ") updated.\n\n" +
                "New frequency: Every " + newFrequency + " days\n\n" +
                "Regards,\n" + companyName;
    }

    private String buildLightweightPOSuccessEmailBody(String customerName, String poNumber, String scheduledOrderId) {
        return (customerName != null ? "Dear " + customerName : "Dear Customer") +
                ",\n\nScheduled order processed successfully.\n\n" +
                "Purchase Order: " + poNumber + "\nScheduled Order ID: " + scheduledOrderId +
                "\n\nRegards,\n" + companyName;
    }

    private String buildLightweightPOFailureEmailBody(String customerName, String scheduledOrderId, String errorMessage) {
        return (customerName != null ? "Dear " + customerName : "Dear Customer") +
                ",\n\nIssue processing scheduled order (ID: " + scheduledOrderId + ").\n\n" +
                "Error: " + errorMessage + "\n\nSupport: " + supportEmail +
                "\n\nRegards,\n" + companyName;
    }
}
