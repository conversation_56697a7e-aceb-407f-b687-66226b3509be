package com.atp.product.utils;

public class Constants {
    // Collection names
    public static final String PRODUCT_COLLECTION_NAME = "category_and_products";
    public static final String SHOPPING_CART_COLLECTION_NAME = "shopping_cart";
    public static final String WISHLIST_COLLECTION_NAME = "wishlist";
    // General constants
    public static final String CATALOG_ID = "catalogId";
    public static final String PARENT_ID = "parentId";
    public static final String CATEGORIES = "categories";
    public static final String SUBCATEGORIES = "subcategories";
    public static final String PRODUCT = "PRODUCT";
    public static final String DTD_WEB_HIERARCHY_ROOT = "WebHierarchyRoot";
    public static final String ACTIVE = "active";
    public static final String IS_DELETED = "isDeleted";
    public static final String BRAND_NAME = "brandName";
    public static final String BRAND_ID = "brandId";
    public static final String BRANDS = "brands";
    public static final String CATEGORY_NAME = "categoryName";
    public static final String CATALOG_TYPE = "catalogType";
    public static final String CATEGORY = "category";
    public static final String SUB_CATEGORY = "subCategory";
    public static final String NAME = "name";
    public static final String PART_NUMBER = "partNumber";
    public static final String PRICE = "price";
    public static final String PRIMARY_IMAGE = "primaryImage";
    public static final String LAST_MODIFIED_DATE = "lastModifiedDate";
    public static final String PART_NUMBER_SLUG = "partNumberSlug";
    public static final String BRAND_SLUG = "brandSlug";
    public static final String CATEGORY_SLUG = "categorySlug";
    public static final String ASSET_MAP = "assetMap";
    public static final String QUANTITY = "quantity";
    public static final String DESCRIPTION = "description";
    public static final String CUSTOMER_CORRELATION_ID = "customerCorrelationId";
    public static final String WISHLIST_FLAG = "isInWishlist";
    public static final String SUB_CATEGORY_SLUG = "subCategorySlug";
    public static final String CROSS_REFERENCE_ID = "crossReferenceId";
    public static final String PRODUCTS="products";

    //ERROR CONSTANTS
    public static final String ERROR_CODE = "errorCode";
    public static final String ERROR_MESSAGE = "errorMessage";
    public static final String INVALID_ARGUMENT_PROVIDED = "Invalid argument provided";
    public static final String INPUT_PARAMETER_NOT_FOUND = "Input parameter not found";
    public static final String ERROR_RESOURCE_NOT_FOUND = "ERROR_RESOURCE_NOT_FOUND";
    public static final String ERROR_DUPLICATE_RESOURCE = "DUPLICATE_RESOURCE"; // New constant for this case

    //Category Service Constants
    public static final String MAKE_LIST = "makeList";
    public static final String MODEL_LIST = "modelList";
    public static final String BRAND_LIST = "brandList";
    public static final String SUB_CATEGORY_LIST = "subCategoryList";
    public static final String VENDOR_LIST = "vendorList";
    public static final String YEAR_LIST = "yearList";
    public static final String CATEGORY_LIST = "categoryList";
    public static final String MAKE = "make";
    public static final String MODEL = "model";
    public static final String YEAR = "year";
    public static final String VENDOR_NAME = "vendorName";
    public static final String ID = "_id";
    public static final String ARRAY_TO_OBJECT = "$arrayToObject";
    public static final String KEY ="k";
    public static final String VALUE ="v";
    public static final String COUNT = "count";
    public static final String ITEM = "item";
    public static final String AS = "as";
    public static final String IN ="in";
    public static final String INPUT ="input";
    public static final String MAP ="map";
    public static final String PROJECT ="project";

    //Atlas Search Parameters
    public static final int MAX_TYPE_AHEAD_RESULTS = 10;
    public static final String AUTOCOMPLETE = "autocomplete";
    public static final String PATH = "path";
    public static final String PATTERN = "pattern";
    public static final String QUERY ="query";
    public static final String FUZZY = "fuzzy";
    public static final String MAX_EDITS = "maxEdits";
    public static final String PREFIX_LENGTH = "prefixLength";
    public static final String FILTER = "filter";
    public static final String COND = "cond";
    public static final String INITIAL_VALUE = "initialValue";
    public static final String PARTS_INVENTORY_DETAIL_ID = "partsInventoryDetailId";
    public static final String BRANCH_CODE = "branch";

    public static final String STATUS_OUT_OF_STOCK = "OUT_OF_STOCK";
    public static final String STATUS_ACTIVE = "ACTIVE";
    public static final String STATUS_CANCELLED = "CANCELLED";
    public static final String STATUS_RETRYING = "RETRYING";
    public static final String STATUS_PROCESSING = "PROCESSING";
    public static final String STATUS_COMPLETED = "COMPLETED";

    // MongoDB Field Names
    public static final String FIELD_ID = "id";
    public static final String FIELD_STATUS = "status";
    public static final String FIELD_NOTES = "notes";
    public static final String FIELD_UPDATED_AT = "updatedAt";
    public static final String FIELD_NEXT_RUN_DATE = "nextRunDate";
    public static final String FIELD_ACTIVE = "active";
    public static final String FIELD_KARMAK_CUSTOMER_ID = "karmakCustomerId";
    public static final String FIELD_ORDERS_PROCESSED = "ordersProcessed";
    public static final String FIELD_LAST_PROCESSED_DATE = "lastProcessedDate";
    public static final String FIELD_FREQUENCY_DAYS = "frequencyDays";
    public static final String FIELD_EMAIL = "email";
    public static final String FIELD_NAME = "name";
    public static final String FIELD_CUSTOMER_CORRELATION_ID = "customerCorrelationId";
    public static final String FIELD_NOTIFY_ON_OUT_OF_STOCK = "notifyOnOutOfStock";
    public static final String FIELD_ORIGINAL_ORDER_ID = "originalOrderId";
    public static final String FIELD_SUBSCRIPTION_DATE = "subscriptionDate";

    // Scheduled Order Messages
    public static final String MSG_PARTS_RESTOCKED = "Parts restocked - ready for processing";
    public static final String MSG_PROCESSED_AFTER_RESTOCK = "Processed after restock";
    public static final String MSG_PROCESSED_SUCCESSFULLY = "Processed successfully";
    public static final String MSG_OUT_OF_STOCK_PREFIX = "Out of stock parts: ";
    public static final String MSG_TEST_OUT_OF_STOCK_PREFIX = "TEST: Out of stock parts: ";
    public static final String MSG_SCHEDULED_ORDER = "Scheduled order";
    public static final String MSG_PARTIAL_SCHEDULED_ORDER = "Partial scheduled order";
    public static final String MSG_REPROCESSED_FROM_OUT_OF_STOCK = "Reprocessed from OUT_OF_STOCK";
    public static final String MSG_CANCELLED_BY_CUSTOMER = "Cancelled by customer";

    // System Users
    public static final String SYSTEM_USER_SCHEDULER = "system@scheduler";
    public static final String SYSTEM_USER_SCHEDULER_PARTIAL = "system@scheduler-partial";
    public static final String SYSTEM_USER_SCHEDULER_RESTOCK = "system@scheduler-restock";

    // Part Number Default
    public static final String PART_NUMBER_NOT_AVAILABLE = "N/A";

    // HTTP Constants
    public static final String CONTENT_TYPE_APPLICATION_JSON = "application/json";
    public static final String HEADER_CONTENT_TYPE = "Content-Type";
    public static final String HEADER_CACHE_CONTROL = "Cache-Control";
    public static final String CACHE_CONTROL_NO_CACHE = "no-cache";
    public static final String HEADER_KARMAK_ACCOUNT_NUMBER = "KarmakAccountNumber";
    public static final String HEADER_OCP_APIM_SUBSCRIPTION_KEY = "Ocp-Apim-Subscription-Key";
    public static final String HEADER_AUTHORIZATION = "Authorization";

    // Default Values
    public static final String DEFAULT_QUANTITY_ZERO = "0";
    public static final String DEFAULT_QUANTITY_ONE = "1";
    public static final String DEFAULT_PRICE = "10.0";
    public static final String DEFAULT_BRANCH = "1";
    public static final String DEFAULT_KARMAK_PART_ID = "1";
    public static final String NULL_STRING = "null";

    // JSON Field Names for API
    public static final String JSON_CUSTOMER_ID = "customerID";
    public static final String JSON_LOCATION_ID = "locationID";
    public static final String JSON_REGION = "region";
    public static final String JSON_PARTS = "parts";
    public static final String JSON_NUMBER = "number";
    public static final String JSON_DESCRIPTION = "description";
    public static final String JSON_EXACT_MATCH = "exactMatch";
    public static final String JSON_SOURCE = "source";
    public static final String JSON_CROSS_REFERENCE = "crossReference";
    public static final String JSON_PAGE_SIZE = "PageSize";
    public static final String JSON_USER_ID = "userid";
    public static final String JSON_PASSWORD = "password";
    public static final String JSON_DEVICE_UUID = "deviceUUID";
    public static final String JSON_ENC = "enc";
    public static final String JSON_USER_PROPERTIES = "userProperties";
    public static final String JSON_STATUS = "status";
    public static final String JSON_TOKEN = "token";

    // Batch Processing
    public static final int DEFAULT_BATCH_SIZE = 100;
    public static final int DEFAULT_MAIN_SCHEDULER_BATCH_SIZE = 50;
    public static final int DEFAULT_BATCH_DELAY_MS = 100;


    // Private constructor to prevent instantiation
    private Constants() {
        throw new UnsupportedOperationException("Constants class cannot be instantiated");
    }
}