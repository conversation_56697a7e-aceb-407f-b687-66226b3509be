spring:
  application:
    name: ATP-product-service
  data:
    mongodb:
#      uri: mongodb+srv://atommxdm:<EMAIL>/?retryWrites=true&w=majority
#      port: 27017
#      database: admindev
#      username: ssapp
#      password: "!ss@app*"
      uri: mongodb://localhost:27017
      port: 27017
      database: atommprod
      username:
      password:
  # OPTIMIZED Gmail SMTP - Fast but reliable (target: 2-3 seconds)
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: nkmkzunnajkhapgh
    protocol: smtp
    test-connection: false
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          # BALANCED fast timeouts - Gmail needs at least 2 seconds
          connectiontimeout: 2000      # 2 seconds (reliable connection)
          timeout: 2500                # 2.5 seconds (SMTP operations)
          writetimeout: 1500           # 1.5 seconds (data writing)
          # TLS optimization for speed
          ssl:
            enable: false              # STARTTLS only
            protocols: TLSv1.2
            ciphersuites: TLS_AES_128_GCM_SHA256  # Fastest cipher
          socketFactory:
            fallback: false
            port: 587
            class: javax.net.ssl.SSLSocketFactory
          # Connection optimization
          connectionpoolsize: 20       # Reasonable pool size
          connectionpooltimeout: 60000 # 1 minute keep-alive
          # Performance optimizations
          localhost: localhost
          localaddress: 127.0.0.1
          ehlo: false                  # Skip EHLO
          quitwait: false              # Don't wait for QUIT
          # Reduce unnecessary overhead
          dsn.notify: NEVER
          dsn.ret: HDRS
          # Skip DNS lookup
          mail.smtp.localhost: localhost
          # Enable partial sends for speed
          mail.smtp.sendpartial: true
        transport:
          protocol: smtp
        debug: false
        # JVM mail optimizations
        mime:
          address:
            strict: false    # Skip address validation for speed
          charset: UTF-8
          decodetext.strict: false

server:
  port: 8081
  servlet:
    context-path: /product-service

# REAL COMPANY INFORMATION - NO HARDCODED VALUES!
company:
  name: "Action Truck Parts"
  support:
    email: "<EMAIL>"
  contact:
    phone: "************"
    address: "1801 Moen Ave, Rockdale, IL 60436"

# EMAIL NOTIFICATION SETTINGS
email:
  notifications:
    enabled: true
aws:
  s3:
    bucket:
      name: atommxdm-prod
    region: us-east-1
  access:
    key: ********************
  secret:
    key: CRcqfITI2hUkKniy37iry6Um7wWUWMOYHHCegMpg
assets:
  root: ssAppArea/assets
logging:
  level:
    org.springframework: WARN
    org.springframework.data.mongodb: WARN
Karmak:
  partSearchURL: https://api.karmak.io/ops/partssalesorder/PartsSearch/Search
  customerURL: https://api.karmak.io/ops/helper/customers?CustomerKey=
  purchaseOrderURL: https://api.karmak.io/ops/partspurchaseorder/PartsPurchaseOrder/Create
  inventoryUpdateUrl: https://api.karmak.io/ops/parts/PartsInventory/UpdatePartQuantity
  AccountNumber: 27487
Ocp-Apim-Subscription-Key: a648453f4c444fcd92e61a23c6de24ae

# Third-party API configuration for scheduled orders
third-party:
  api:
    url: http://localhost:8080/server/api/data-entities/create
    login-url: http://localhost:8080/server/rest/auth/login
    username: YashSharma
    password: Indianic@123

