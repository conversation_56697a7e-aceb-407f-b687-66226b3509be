<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scheduled Order Temporarily Delayed</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        
        .email-container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px 40px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .content {
            padding: 40px;
        }
        
        .greeting {
            font-size: 16px;
            color: #2c3e50;
            margin-bottom: 20px;
        }
        
        .message {
            color: #555555;
            margin-bottom: 30px;
            font-size: 15px;
        }
        
        .alert-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 20px;
            margin: 25px 0;
        }
        
        .alert-title {
            font-weight: 600;
            color: #856404;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .parts-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .parts-table th {
            background-color: #34495e;
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
        }
        
        .parts-table td {
            padding: 12px;
            border-bottom: 1px solid #e9ecef;
            color: #555555;
            font-size: 14px;
        }
        
        .parts-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .parts-table tr:hover {
            background-color: #e8f4f8;
        }
        
        .part-number {
            font-weight: 600;
            color: #2c3e50;
        }
        
        .status-badge {
            background-color: #dc3545;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .next-steps {
            background-color: #e8f4f8;
            border-left: 4px solid #17a2b8;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 6px 6px 0;
        }
        
        .next-steps h3 {
            color: #0c5460;
            margin: 0 0 15px 0;
            font-size: 16px;
        }
        
        .next-steps ul {
            margin: 0;
            padding-left: 20px;
            color: #155160;
        }
        
        .next-steps li {
            margin-bottom: 8px;
        }
        
        .contact-info {
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 20px;
            margin: 25px 0;
            text-align: center;
        }
        
        .contact-info h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 16px;
        }
        
        .contact-link {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
        }
        
        .contact-link:hover {
            text-decoration: underline;
        }
        
        .footer {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 30px 40px;
            text-align: center;
        }
        
        .footer .company-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .footer .contact-details {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .order-summary {
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .order-summary h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        
        .order-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 8px 0;
            font-size: 14px;
            min-height: 20px;
            width: 100%;
        }

        .order-details .col:first-child {
            text-align: left;
            flex: 0 0 auto;
        }

        .order-details .col:last-child {
            text-align: right;
            flex: 1 1 auto;
            margin-left: 20px;
        }

        .label {
            font-weight: 600;
            color: #555555;
        }

        .value {
            color: #2c3e50;
            font-weight: 500;
        }

        .row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .col {
            flex: 1;
        }

        .col:last-child {
            text-align: right;
        }

        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 0;
            }
            
            .header, .content, .footer {
                padding: 20px;
            }
            
            .parts-table th, .parts-table td {
                padding: 8px 6px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>🔔 Order Status Update</h1>
        </div>
        
        <!-- Content -->
        <div class="content">
            <div class="greeting">
                {{CUSTOMER_GREETING}}
            </div>
            
            <div class="message">
                We are writing to inform you about a temporary delay with your scheduled order. 
                After conducting our comprehensive inventory review, we have identified that some 
                items are currently out of stock and require restocking from our suppliers.
            </div>
            
            <!-- Order Summary -->
            <div class="order-summary">
                <h4>📋 Scheduled Order Details</h4>
                <table style="width: 100%; border-collapse: collapse;">
<!--                    <tr>-->
<!--                        <td style="padding: 8px 0; font-weight: 600; color: #555555;">Customer ID:</td>-->
<!--                        <td style="padding: 8px 0; text-align: right; color: #2c3e50; font-weight: 500;">{{CUSTOMER_ID}}</td>-->
<!--                    </tr>-->
<!--                    <tr>-->
<!--                        <td style="padding: 8px 0; font-weight: 600; color: #555555;">Customer Correlation ID:</td>-->
<!--                        <td style="padding: 8px 0; text-align: right; color: #2c3e50; font-weight: 500;">{{CUSTOMER_CORRELATION_ID}}</td>-->
<!--                    </tr>-->
                    <tr>
                        <td style="padding: 8px 0; font-weight: 600; color: #555555;">Order ID:</td>
                        <td style="padding: 8px 0; text-align: right; color: #2c3e50; font-weight: 500;">{{ORIGINAL_ORDER_ID}}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: 600; color: #555555;">Subscription Date:</td>
                        <td style="padding: 8px 0; text-align: right; color: #2c3e50; font-weight: 500;">{{SUBSCRIPTION_DATE}}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: 600; color: #555555;">Frequency:</td>
                        <td style="padding: 8px 0; text-align: right; color: #2c3e50; font-weight: 500;">Every {{FREQUENCY_DAYS}} days</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: 600; color: #555555;">Date Processed:</td>
                        <td style="padding: 8px 0; text-align: right; color: #2c3e50; font-weight: 500;">{{PROCESS_DATE}}</td>
                    </tr>
                </table>
            </div>
            
            <!-- Alert Box -->
            <div class="alert-box">
                <div class="alert-title">⚠️ Items Currently Out of Stock</div>
                The following {{UNAVAILABLE_COUNT}} item(s) are temporarily unavailable:
            </div>
            
            <!-- Parts Table -->
            <table class="parts-table">
                <thead>
                    <tr>
                        <th>Part Number</th>
                        <th>Description</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    {{PARTS_TABLE_ROWS}}
                </tbody>
            </table>
            
            {{ADDITIONAL_PARTS_MESSAGE}}
            
            <!-- Next Steps -->
            <div class="next-steps">
                <h3>🚀 Next Steps & What We're Doing</h3>
                <ul>
                    <li><strong>Supplier Coordination:</strong> We are actively working with our suppliers to expedite restocking</li>
                    <li><strong>Automatic Processing:</strong> Your order will be automatically processed once items become available</li>
                    <li><strong>Immediate Notification:</strong> You will receive instant notification when your order ships</li>
                    <li><strong>No Action Required:</strong> You don't need to do anything - we'll handle everything</li>
                    <li><strong>Priority Status:</strong> Your order has been flagged for priority processing</li>
                </ul>
            </div>
            
            <div class="message">
                We sincerely apologize for any inconvenience this delay may cause. Your satisfaction 
                is our highest priority, and we are committed to fulfilling your order as quickly as 
                possible while maintaining our quality standards.
            </div>
            
            <!-- Contact Information -->
            <div class="contact-info">
                <h3>💬 Need Assistance?</h3>
                <p>If you have any questions or would like to discuss alternative options, 
                our customer support team is here to help.</p>
                <p>
                    📧 Email: <a href="mailto:{{SUPPORT_EMAIL}}" class="contact-link">{{SUPPORT_EMAIL}}</a><br>
                    🔄 Reply directly to this email for fastest response
                </p>
            </div>
            
            <div class="message" style="text-align: center; margin-top: 30px; font-weight: 500;">
                Thank you for your patience and continued business. 🙏
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="company-name">{{COMPANY_NAME}}</div>
            <div class="contact-details">
                Customer Service Team<br>
                📧 {{SUPPORT_EMAIL}} | 🌐 Professional Parts & Service Solutions
            </div>
        </div>
    </div>
</body>
</html>
